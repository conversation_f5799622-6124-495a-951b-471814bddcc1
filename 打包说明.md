# 媒体预算优化工具 - EXE打包说明

## 📦 打包方法

### 方法一：使用自动打包脚本（推荐）

1. **运行Python打包脚本**：
   ```bash
   python build_exe.py
   ```

2. **或者运行批处理文件**：
   ```bash
   quick_build.bat
   ```

### 方法二：手动打包

1. **安装PyInstaller**：
   ```bash
   pip install pyinstaller openpyxl pandas numpy
   ```

2. **使用配置文件打包**：
   ```bash
   pyinstaller media_optimizer.spec
   ```

3. **或者使用命令行打包**：
   ```bash
   pyinstaller --onefile --windowed --name="媒体预算优化工具" universal_media_optimizer_v2.py
   ```

## 📁 输出文件

打包完成后，在 `dist` 目录下会生成：

- `媒体预算优化工具.exe` - 主程序
- `使用说明.txt` - 使用说明文档
- `Gatorade simulation tool_cal.xlsx` - 示例Excel文件（如果存在）

## ⚙️ 打包配置说明

### PyInstaller参数说明

- `--onefile` - 打包成单个exe文件
- `--windowed` - 隐藏控制台窗口
- `--name` - 指定exe文件名
- `--add-data` - 添加数据文件
- `--icon` - 指定应用图标

### 包含的模块

- **界面模块**: tkinter, tkinter.ttk
- **Excel处理**: openpyxl
- **数据处理**: pandas, numpy
- **优化算法**: 自定义优化引擎
- **其他**: threading, json, datetime等

## 🎯 优化建议

### 减小文件大小

1. **排除不需要的模块**：
   ```python
   excludes=[
       'matplotlib', 'scipy', 'IPython', 
       'jupyter', 'pytest', 'setuptools'
   ]
   ```

2. **使用UPX压缩**：
   ```bash
   pyinstaller --upx-dir=/path/to/upx media_optimizer.spec
   ```

### 提高启动速度

1. **使用--onedir模式**（文件较多但启动快）：
   ```bash
   pyinstaller --onedir --windowed media_optimizer.spec
   ```

2. **预编译模块**：
   ```bash
   python -m compileall .
   ```

## 🔧 故障排除

### 常见问题

1. **模块导入错误**：
   - 在spec文件的hiddenimports中添加缺失模块
   - 检查模块名称是否正确

2. **文件路径问题**：
   - 使用相对路径
   - 确保数据文件正确添加到datas中

3. **Excel文件读取失败**：
   - 确保openpyxl正确打包
   - 检查Excel文件格式

4. **界面显示异常**：
   - 确保tkinter模块完整打包
   - 检查字体和编码设置

### 调试方法

1. **启用控制台输出**：
   ```python
   console=True  # 在spec文件中设置
   ```

2. **添加调试信息**：
   ```python
   debug=True  # 在spec文件中设置
   ```

3. **检查依赖关系**：
   ```bash
   pyinstaller --collect-all openpyxl media_optimizer.spec
   ```

## 📊 性能优化

### 文件大小对比

| 打包方式 | 文件大小 | 启动时间 | 兼容性 |
|---------|---------|---------|--------|
| --onefile | ~50MB | 较慢 | 最佳 |
| --onedir | ~100MB | 较快 | 良好 |

### 推荐配置

**生产环境**：
```bash
pyinstaller --onefile --windowed --optimize=2 media_optimizer.spec
```

**开发测试**：
```bash
pyinstaller --onedir --console media_optimizer.spec
```

## 🚀 分发说明

### 系统要求

- **操作系统**: Windows 7/8/10/11 (64位)
- **内存**: 建议4GB以上
- **磁盘空间**: 100MB可用空间
- **其他**: Microsoft Visual C++ Redistributable

### 分发包内容

```
媒体预算优化工具/
├── 媒体预算优化工具.exe     # 主程序
├── 使用说明.txt            # 使用说明
├── Gatorade simulation tool_cal.xlsx  # 示例文件
└── README.md               # 详细说明（可选）
```

### 安装说明

1. 解压分发包到任意目录
2. 双击 `媒体预算优化工具.exe` 启动
3. 首次运行可能需要较长时间
4. 如有杀毒软件误报，请添加信任

## 💡 使用提示

1. **首次运行**：可能需要30秒-2分钟启动时间
2. **文件路径**：避免包含特殊字符的路径
3. **Excel格式**：确保使用.xlsx格式
4. **权限问题**：如有问题可尝试以管理员身份运行
5. **网络环境**：程序无需网络连接即可运行

## 📞 技术支持

如果在打包或使用过程中遇到问题：

1. 检查Python环境和依赖包版本
2. 查看错误日志和控制台输出
3. 尝试重新安装PyInstaller
4. 联系开发团队获取技术支持

---

**版本**: 2.0.0  
**更新日期**: 2024年6月19日  
**开发团队**: Media Optimization Team
