import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import win32com.client
import time
from datetime import datetime
import threading

class UniversalMediaOptimizerV2:
    def __init__(self, root):
        self.root = root
        self.root.title("通用媒体预算优化工具 v2.0")
        self.root.geometry("900x800")
        
        # 配置变量
        self.config = {
            "excel_file": "",
            "worksheet_name": "calculation",
            "input_start_row": 38,
            "input_end_row": 57,
            "input_start_col": 3,  # C列
            "input_end_col": 18,   # R列
            "output_col": 21,      # U列
            "output_start_row": 38,  # 输出起始行，默认与输入一致
            "output_end_row": 57,    # 输出结束行，默认与输入一致
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 5,
            "baseline_allocation": "equal",  # equal, custom
            "custom_baseline_ratios": {},  # 自定义基准配比
            "channel_constraints": {}
        }
        
        self.channels = []
        self.excel = None
        self.workbook = None
        self.worksheet = None
        
        self.create_widgets()
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建滚动框架
        canvas = tk.Canvas(self.root)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 主框架
        main_frame = ttk.Frame(scrollable_frame, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 1. 文件设置区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 文件设置", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar(value=self.config["excel_file"])
        ttk.Entry(file_frame, textvariable=self.file_var, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        
        ttk.Label(file_frame, text="工作表名称:").grid(row=1, column=0, sticky=tk.W)
        self.worksheet_var = tk.StringVar(value=self.config["worksheet_name"])
        ttk.Entry(file_frame, textvariable=self.worksheet_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 2. 数据区域设置
        area_frame = ttk.LabelFrame(main_frame, text="2. 数据区域设置", padding="10")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 输入区域
        ttk.Label(area_frame, text="输入区域:").grid(row=0, column=0, sticky=tk.W)
        input_frame = ttk.Frame(area_frame)
        input_frame.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(input_frame, text="行:").grid(row=0, column=0)
        self.start_row_var = tk.IntVar(value=self.config["input_start_row"])
        ttk.Entry(input_frame, textvariable=self.start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(input_frame, text="到").grid(row=0, column=2)
        self.end_row_var = tk.IntVar(value=self.config["input_end_row"])
        ttk.Entry(input_frame, textvariable=self.end_row_var, width=5).grid(row=0, column=3)
        
        ttk.Label(input_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.start_col_var = tk.IntVar(value=self.config["input_start_col"])
        ttk.Entry(input_frame, textvariable=self.start_col_var, width=5).grid(row=0, column=5)
        ttk.Label(input_frame, text="到").grid(row=0, column=6)
        self.end_col_var = tk.IntVar(value=self.config["input_end_col"])
        ttk.Entry(input_frame, textvariable=self.end_col_var, width=5).grid(row=0, column=7)
        
        # 输出区域
        ttk.Label(area_frame, text="输出区域:").grid(row=1, column=0, sticky=tk.W)
        output_frame = ttk.Frame(area_frame)
        output_frame.grid(row=1, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(output_frame, text="行:").grid(row=0, column=0)
        self.output_start_row_var = tk.IntVar(value=self.config["output_start_row"])
        ttk.Entry(output_frame, textvariable=self.output_start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(output_frame, text="到").grid(row=0, column=2)
        self.output_end_row_var = tk.IntVar(value=self.config["output_end_row"])
        ttk.Entry(output_frame, textvariable=self.output_end_row_var, width=5).grid(row=0, column=3)
        
        ttk.Label(output_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.output_col_var = tk.IntVar(value=self.config["output_col"])
        ttk.Entry(output_frame, textvariable=self.output_col_var, width=5).grid(row=0, column=5)
        
        # 同步按钮
        ttk.Button(output_frame, text="同步输入区域", 
                  command=self.sync_output_with_input).grid(row=0, column=6, padx=(10,0))
        
        # 渠道名称行
        ttk.Label(area_frame, text="渠道名称行:").grid(row=2, column=0, sticky=tk.W)
        self.channel_row_var = tk.IntVar(value=self.config["channel_names_row"])
        ttk.Entry(area_frame, textvariable=self.channel_row_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5)
        
        # 3. 预算设置
        budget_frame = ttk.LabelFrame(main_frame, text="3. 预算设置", padding="10")
        budget_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(budget_frame, text="总预算:").grid(row=0, column=0, sticky=tk.W)
        self.budget_var = tk.IntVar(value=self.config["total_budget"])
        ttk.Entry(budget_frame, textvariable=self.budget_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(budget_frame, text="优化方案数量:").grid(row=1, column=0, sticky=tk.W)
        self.schemes_var = tk.IntVar(value=self.config["optimization_schemes"])
        ttk.Entry(budget_frame, textvariable=self.schemes_var, width=5).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 基准方案设置
        ttk.Label(budget_frame, text="基准方案:").grid(row=2, column=0, sticky=tk.W)
        self.baseline_var = tk.StringVar(value=self.config["baseline_allocation"])
        baseline_combo = ttk.Combobox(budget_frame, textvariable=self.baseline_var, 
                                    values=["equal", "custom"], width=12)
        baseline_combo.grid(row=2, column=1, sticky=tk.W, padx=5)
        baseline_combo.bind("<<ComboboxSelected>>", self.on_baseline_changed)
        
        # 自定义基准方案按钮
        self.custom_baseline_btn = ttk.Button(budget_frame, text="设置自定义配比", 
                                            command=self.set_custom_baseline, state="disabled")
        self.custom_baseline_btn.grid(row=2, column=2, padx=5)
        
        # 4. 渠道约束设置
        constraint_frame = ttk.LabelFrame(main_frame, text="4. 渠道约束设置", padding="10")
        constraint_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        button_constraint_frame = ttk.Frame(constraint_frame)
        button_constraint_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        ttk.Button(button_constraint_frame, text="读取渠道信息", 
                  command=self.load_channels).grid(row=0, column=0, pady=5)
        ttk.Button(button_constraint_frame, text="设置渠道约束", 
                  command=self.set_channel_constraints).grid(row=0, column=1, padx=5, pady=5)
        
        # 默认约束说明
        ttk.Label(button_constraint_frame, text="默认约束范围: 2%-60% (可修改)", 
                 foreground="blue").grid(row=0, column=2, padx=10)
        
        # 渠道信息显示
        self.channel_text = tk.Text(constraint_frame, height=10, width=80)
        self.channel_text.grid(row=1, column=0, columnspan=2, pady=5)
        
        channel_scrollbar = ttk.Scrollbar(constraint_frame, orient="vertical", command=self.channel_text.yview)
        channel_scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.channel_text.configure(yscrollcommand=channel_scrollbar.set)
        
        # 5. 操作按钮
        button_frame = ttk.LabelFrame(main_frame, text="5. 操作", padding="10")
        button_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Button(button_frame, text="保存配置", 
                  command=self.save_config).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="加载配置", 
                  command=self.load_config_file).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="开始优化", 
                  command=self.start_optimization).grid(row=0, column=2, padx=5)
        
        # 进度条和状态
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=6, column=0, columnspan=2)
        
        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def sync_output_with_input(self):
        """同步输出区域与输入区域"""
        self.output_start_row_var.set(self.start_row_var.get())
        self.output_end_row_var.set(self.end_row_var.get())
    
    def on_baseline_changed(self, event=None):
        """基准方案选择改变时的回调"""
        if self.baseline_var.get() == "custom":
            self.custom_baseline_btn.config(state="normal")
        else:
            self.custom_baseline_btn.config(state="disabled")
    
    def set_custom_baseline(self):
        """设置自定义基准配比"""
        if not self.channels:
            messagebox.showwarning("警告", "请先读取渠道信息")
            return
        
        # 创建自定义基准设置窗口
        custom_window = tk.Toplevel(self.root)
        custom_window.title("设置自定义基准配比")
        custom_window.geometry("500x600")
        
        ttk.Label(custom_window, text="设置各渠道的基准预算配比（总和应为100%）", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        # 创建滚动框架
        canvas = tk.Canvas(custom_window)
        scrollbar = ttk.Scrollbar(custom_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 渠道配比输入
        ratio_vars = {}
        for i, channel in enumerate(self.channels):
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill=tk.X, padx=10, pady=2)
            
            ttk.Label(frame, text=f"{channel}:", width=20).pack(side=tk.LEFT)
            
            # 获取已保存的配比或默认值
            default_ratio = self.config["custom_baseline_ratios"].get(channel, 100.0 / len(self.channels))
            ratio_var = tk.DoubleVar(value=default_ratio)
            ratio_vars[channel] = ratio_var
            
            ttk.Entry(frame, textvariable=ratio_var, width=10).pack(side=tk.LEFT, padx=5)
            ttk.Label(frame, text="%").pack(side=tk.LEFT)
        
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")
        
        # 按钮框架
        button_frame = ttk.Frame(custom_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def check_total():
            """检查总配比"""
            total = sum(var.get() for var in ratio_vars.values())
            total_label.config(text=f"总配比: {total:.1f}%", 
                             foreground="green" if abs(total - 100) < 0.1 else "red")
        
        def save_custom_baseline():
            """保存自定义基准配比"""
            total = sum(var.get() for var in ratio_vars.values())
            if abs(total - 100) > 0.1:
                messagebox.showerror("错误", f"总配比必须为100%，当前为{total:.1f}%")
                return
            
            # 保存配比
            self.config["custom_baseline_ratios"] = {
                channel: var.get() for channel, var in ratio_vars.items()
            }
            
            messagebox.showinfo("成功", "自定义基准配比已保存")
            custom_window.destroy()
        
        def auto_equal():
            """自动平均分配"""
            equal_ratio = 100.0 / len(self.channels)
            for var in ratio_vars.values():
                var.set(equal_ratio)
            check_total()
        
        ttk.Button(button_frame, text="自动平均分配", command=auto_equal).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="检查总配比", command=check_total).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存", command=save_custom_baseline).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=custom_window.destroy).pack(side=tk.LEFT, padx=5)
        
        # 总配比显示
        total_label = ttk.Label(button_frame, text="总配比: 0.0%")
        total_label.pack(side=tk.RIGHT, padx=5)
        
        # 初始检查
        check_total()
    
    def browse_file(self):
        """浏览选择Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)

    def load_channels(self):
        """读取渠道信息"""
        try:
            excel_file = self.file_var.get()
            if not excel_file or not os.path.exists(excel_file):
                messagebox.showerror("错误", "请先选择有效的Excel文件")
                return

            self.status_var.set("正在读取渠道信息...")

            # 初始化Excel
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False

            try:
                workbook = excel.Workbooks.Open(os.path.abspath(excel_file))
                worksheet = workbook.Worksheets(self.worksheet_var.get())

                # 读取渠道名称
                self.channels = []
                start_col = self.start_col_var.get()
                end_col = self.end_col_var.get()
                channel_row = self.channel_row_var.get()

                for col in range(start_col, end_col + 1):
                    cell_value = worksheet.Cells(channel_row, col).Value
                    if cell_value:
                        self.channels.append(str(cell_value).strip())

                # 显示渠道信息
                self.channel_text.delete(1.0, tk.END)
                self.channel_text.insert(tk.END, f"成功读取到 {len(self.channels)} 个媒体渠道:\n\n")
                for i, channel in enumerate(self.channels, 1):
                    constraint_info = ""
                    if channel in self.config["channel_constraints"]:
                        constraint = self.config["channel_constraints"][channel]
                        constraint_info = f" [约束: {constraint['min']:.1%}-{constraint['max']:.1%}]"

                    custom_info = ""
                    if channel in self.config["custom_baseline_ratios"]:
                        ratio = self.config["custom_baseline_ratios"][channel]
                        custom_info = f" [自定义基准: {ratio:.1f}%]"

                    self.channel_text.insert(tk.END, f"{i:2d}. {channel}{constraint_info}{custom_info}\n")

                self.status_var.set(f"成功读取 {len(self.channels)} 个渠道")

            finally:
                workbook.Close(SaveChanges=False)
                excel.Quit()

        except Exception as e:
            messagebox.showerror("错误", f"读取渠道信息失败: {str(e)}")
            self.status_var.set("读取失败")

    def set_channel_constraints(self):
        """设置渠道约束"""
        if not self.channels:
            messagebox.showwarning("警告", "请先读取渠道信息")
            return

        # 创建约束设置窗口
        constraint_window = tk.Toplevel(self.root)
        constraint_window.title("设置渠道约束")
        constraint_window.geometry("700x600")

        # 创建表格
        columns = ("渠道名称", "最小比例(%)", "最大比例(%)")
        tree = ttk.Treeview(constraint_window, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=200)

        # 添加渠道数据
        for channel in self.channels:
            if channel in self.config["channel_constraints"]:
                constraint = self.config["channel_constraints"][channel]
                min_pct = constraint["min"] * 100
                max_pct = constraint["max"] * 100
            else:
                # 使用默认约束范围 2%-60%
                min_pct = 2.0
                max_pct = 60.0

            tree.insert("", tk.END, values=(channel, f"{min_pct:.1f}", f"{max_pct:.1f}"))

        tree.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

        # 编辑功能
        def edit_constraint():
            selected = tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请选择要编辑的渠道")
                return

            item = tree.item(selected[0])
            channel = item["values"][0]

            # 创建编辑对话框
            edit_window = tk.Toplevel(constraint_window)
            edit_window.title(f"编辑约束 - {channel}")
            edit_window.geometry("350x200")

            ttk.Label(edit_window, text=f"渠道: {channel}", font=("Arial", 12, "bold")).pack(pady=10)

            frame = ttk.Frame(edit_window)
            frame.pack(pady=10)

            ttk.Label(frame, text="最小比例(%):").grid(row=0, column=0, sticky=tk.W, pady=5)
            min_var = tk.DoubleVar(value=float(item["values"][1]))
            ttk.Entry(frame, textvariable=min_var, width=10).grid(row=0, column=1, padx=5, pady=5)

            ttk.Label(frame, text="最大比例(%):").grid(row=1, column=0, sticky=tk.W, pady=5)
            max_var = tk.DoubleVar(value=float(item["values"][2]))
            ttk.Entry(frame, textvariable=max_var, width=10).grid(row=1, column=1, padx=5, pady=5)

            def save_constraint():
                min_val = min_var.get() / 100
                max_val = max_var.get() / 100

                if min_val < 0 or max_val > 1 or min_val > max_val:
                    messagebox.showerror("错误", "约束值无效（最小值应≥0%，最大值应≤100%，且最小值≤最大值）")
                    return

                self.config["channel_constraints"][channel] = {
                    "min": min_val,
                    "max": max_val
                }

                # 更新树形视图
                tree.item(selected[0], values=(channel, f"{min_val*100:.1f}", f"{max_val*100:.1f}"))
                edit_window.destroy()
                messagebox.showinfo("成功", f"已更新 {channel} 的约束")

            button_frame = ttk.Frame(edit_window)
            button_frame.pack(pady=20)
            ttk.Button(button_frame, text="保存", command=save_constraint).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=edit_window.destroy).pack(side=tk.LEFT, padx=5)

        def reset_all_constraints():
            """重置所有约束为默认值"""
            if messagebox.askyesno("确认", "确定要重置所有渠道约束为默认值（2%-60%）吗？"):
                for item in tree.get_children():
                    channel = tree.item(item)["values"][0]
                    tree.item(item, values=(channel, "2.0", "60.0"))
                    if channel in self.config["channel_constraints"]:
                        del self.config["channel_constraints"][channel]
                messagebox.showinfo("成功", "已重置所有约束为默认值")

        def batch_set_constraints():
            """批量设置约束"""
            batch_window = tk.Toplevel(constraint_window)
            batch_window.title("批量设置约束")
            batch_window.geometry("400x300")

            ttk.Label(batch_window, text="批量设置渠道约束", font=("Arial", 12, "bold")).pack(pady=10)

            # 预设方案
            preset_frame = ttk.LabelFrame(batch_window, text="预设方案", padding="10")
            preset_frame.pack(fill=tk.X, padx=10, pady=5)

            def apply_preset(min_pct, max_pct, description):
                if messagebox.askyesno("确认", f"确定要将所有渠道约束设置为 {description} 吗？"):
                    for item in tree.get_children():
                        channel = tree.item(item)["values"][0]
                        tree.item(item, values=(channel, f"{min_pct:.1f}", f"{max_pct:.1f}"))
                        self.config["channel_constraints"][channel] = {
                            "min": min_pct / 100,
                            "max": max_pct / 100
                        }
                    messagebox.showinfo("成功", f"已应用 {description}")
                    batch_window.destroy()

            ttk.Button(preset_frame, text="保守型 (2%-60%)",
                      command=lambda: apply_preset(2.0, 60.0, "保守型约束")).pack(side=tk.LEFT, padx=5)
            ttk.Button(preset_frame, text="均衡型 (5%-40%)",
                      command=lambda: apply_preset(5.0, 40.0, "均衡型约束")).pack(side=tk.LEFT, padx=5)
            ttk.Button(preset_frame, text="激进型 (1%-80%)",
                      command=lambda: apply_preset(1.0, 80.0, "激进型约束")).pack(side=tk.LEFT, padx=5)

            # 自定义批量设置
            custom_frame = ttk.LabelFrame(batch_window, text="自定义批量设置", padding="10")
            custom_frame.pack(fill=tk.X, padx=10, pady=5)

            input_frame = ttk.Frame(custom_frame)
            input_frame.pack()

            ttk.Label(input_frame, text="最小比例(%):").grid(row=0, column=0, sticky=tk.W, pady=5)
            min_var = tk.DoubleVar(value=2.0)
            ttk.Entry(input_frame, textvariable=min_var, width=10).grid(row=0, column=1, padx=5, pady=5)

            ttk.Label(input_frame, text="最大比例(%):").grid(row=1, column=0, sticky=tk.W, pady=5)
            max_var = tk.DoubleVar(value=60.0)
            ttk.Entry(input_frame, textvariable=max_var, width=10).grid(row=1, column=1, padx=5, pady=5)

            def apply_custom():
                min_val = min_var.get()
                max_val = max_var.get()

                if min_val < 0 or max_val > 100 or min_val > max_val:
                    messagebox.showerror("错误", "约束值无效")
                    return

                if messagebox.askyesno("确认", f"确定要将所有渠道约束设置为 {min_val:.1f}%-{max_val:.1f}% 吗？"):
                    for item in tree.get_children():
                        channel = tree.item(item)["values"][0]
                        tree.item(item, values=(channel, f"{min_val:.1f}", f"{max_val:.1f}"))
                        self.config["channel_constraints"][channel] = {
                            "min": min_val / 100,
                            "max": max_val / 100
                        }
                    messagebox.showinfo("成功", "已应用自定义约束")
                    batch_window.destroy()

            ttk.Button(input_frame, text="应用", command=apply_custom).grid(row=2, column=0, columnspan=2, pady=10)

            # 渠道类型批量设置
            type_frame = ttk.LabelFrame(batch_window, text="按渠道类型设置", padding="10")
            type_frame.pack(fill=tk.X, padx=10, pady=5)

            def set_by_type():
                type_window = tk.Toplevel(batch_window)
                type_window.title("按渠道类型设置约束")
                type_window.geometry("500x400")

                # 定义渠道类型
                channel_types = {
                    "数字媒体": ["SearchVolume", "Display", "RTBOCPX", "DigitalCoop"],
                    "社交媒体": ["DouyinKOL", "RedKOL", "WeiboKOL", "DouyinPush", "WeiboPush"],
                    "视频广告": ["OTTPreroll", "OTVPreroll"],
                    "线下媒体": ["BuildingLCD", "Metro", "CreativeOutdoor"],
                    "赞助活动": ["SponsorEvent", "SponsorDrama"]
                }

                ttk.Label(type_window, text="为不同类型的渠道设置不同约束",
                         font=("Arial", 12, "bold")).pack(pady=10)

                type_vars = {}
                for type_name, channels_in_type in channel_types.items():
                    frame = ttk.LabelFrame(type_window, text=type_name, padding="5")
                    frame.pack(fill=tk.X, padx=10, pady=5)

                    input_frame = ttk.Frame(frame)
                    input_frame.pack()

                    ttk.Label(input_frame, text="最小%:").grid(row=0, column=0)
                    min_var = tk.DoubleVar(value=2.0)
                    ttk.Entry(input_frame, textvariable=min_var, width=8).grid(row=0, column=1, padx=2)

                    ttk.Label(input_frame, text="最大%:").grid(row=0, column=2)
                    max_var = tk.DoubleVar(value=60.0)
                    ttk.Entry(input_frame, textvariable=max_var, width=8).grid(row=0, column=3, padx=2)

                    type_vars[type_name] = (min_var, max_var, channels_in_type)

                def apply_type_constraints():
                    for type_name, (min_var, max_var, channels_in_type) in type_vars.items():
                        min_val = min_var.get()
                        max_val = max_var.get()

                        if min_val < 0 or max_val > 100 or min_val > max_val:
                            messagebox.showerror("错误", f"{type_name} 的约束值无效")
                            return

                        # 应用到对应渠道
                        for item in tree.get_children():
                            channel = tree.item(item)["values"][0]
                            if channel in channels_in_type:
                                tree.item(item, values=(channel, f"{min_val:.1f}", f"{max_val:.1f}"))
                                self.config["channel_constraints"][channel] = {
                                    "min": min_val / 100,
                                    "max": max_val / 100
                                }

                    messagebox.showinfo("成功", "已按渠道类型应用约束")
                    type_window.destroy()

                ttk.Button(type_window, text="应用设置", command=apply_type_constraints).pack(pady=10)

            ttk.Button(type_frame, text="按渠道类型设置", command=set_by_type).pack()

        button_frame = ttk.Frame(constraint_window)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="编辑选中项", command=edit_constraint).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="批量设置", command=batch_set_constraints).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置所有约束", command=reset_all_constraints).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=constraint_window.destroy).pack(side=tk.LEFT, padx=5)

    def save_config(self):
        """保存配置"""
        self.config.update({
            "excel_file": self.file_var.get(),
            "worksheet_name": self.worksheet_var.get(),
            "input_start_row": self.start_row_var.get(),
            "input_end_row": self.end_row_var.get(),
            "input_start_col": self.start_col_var.get(),
            "input_end_col": self.end_col_var.get(),
            "output_col": self.output_col_var.get(),
            "output_start_row": self.output_start_row_var.get(),
            "output_end_row": self.output_end_row_var.get(),
            "channel_names_row": self.channel_row_var.get(),
            "total_budget": self.budget_var.get(),
            "optimization_schemes": self.schemes_var.get(),
            "baseline_allocation": self.baseline_var.get()
        })

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载默认配置"""
        config_file = "media_optimizer_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                    self.update_ui_from_config()
            except:
                pass

    def load_config_file(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                    self.update_ui_from_config()
                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def update_ui_from_config(self):
        """从配置更新界面"""
        self.file_var.set(self.config.get("excel_file", ""))
        self.worksheet_var.set(self.config.get("worksheet_name", "calculation"))
        self.start_row_var.set(self.config.get("input_start_row", 38))
        self.end_row_var.set(self.config.get("input_end_row", 57))
        self.start_col_var.set(self.config.get("input_start_col", 3))
        self.end_col_var.set(self.config.get("input_end_col", 18))
        self.output_col_var.set(self.config.get("output_col", 21))
        self.output_start_row_var.set(self.config.get("output_start_row", 38))
        self.output_end_row_var.set(self.config.get("output_end_row", 57))
        self.channel_row_var.set(self.config.get("channel_names_row", 1))
        self.budget_var.set(self.config.get("total_budget", 1000000))
        self.schemes_var.set(self.config.get("optimization_schemes", 5))
        self.baseline_var.set(self.config.get("baseline_allocation", "equal"))
        self.on_baseline_changed()  # 更新自定义基准按钮状态

    def start_optimization(self):
        """开始优化"""
        # 验证输入
        if not self.file_var.get() or not os.path.exists(self.file_var.get()):
            messagebox.showerror("错误", "请选择有效的Excel文件")
            return

        if not self.channels:
            messagebox.showerror("错误", "请先读取渠道信息")
            return

        # 验证自定义基准配比
        if self.baseline_var.get() == "custom":
            if not self.config["custom_baseline_ratios"]:
                messagebox.showerror("错误", "请先设置自定义基准配比")
                return

            total_ratio = sum(self.config["custom_baseline_ratios"].values())
            if abs(total_ratio - 100) > 0.1:
                messagebox.showerror("错误", f"自定义基准配比总和应为100%，当前为{total_ratio:.1f}%")
                return

        # 在新线程中运行优化
        self.progress.start()
        self.status_var.set("正在优化...")

        optimization_thread = threading.Thread(target=self.run_optimization)
        optimization_thread.daemon = True
        optimization_thread.start()

    def run_optimization(self):
        """运行优化（在后台线程中）"""
        try:
            # 调用优化引擎
            from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

            engine = UniversalOptimizerEngineV2(self.config, self.channels)
            results = engine.optimize()

            # 在主线程中更新UI
            self.root.after(0, self.optimization_complete, results)

        except Exception as e:
            self.root.after(0, self.optimization_error, str(e))

    def optimization_complete(self, results):
        """优化完成"""
        self.progress.stop()
        self.status_var.set("优化完成")

        if results and "output_file" in results:
            messagebox.showinfo("成功", f"优化完成！\n生成了 {len(results['results'])} 个方案\n结果已保存到: {results['output_file']}")
        else:
            messagebox.showinfo("成功", "优化完成！")

    def optimization_error(self, error_msg):
        """优化出错"""
        self.progress.stop()
        self.status_var.set("优化失败")
        messagebox.showerror("错误", f"优化失败: {error_msg}")


if __name__ == "__main__":
    root = tk.Tk()
    app = UniversalMediaOptimizerV2(root)
    root.mainloop()
