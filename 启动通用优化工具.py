#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用媒体预算优化工具启动器
作者: AI Assistant
版本: 1.0
日期: 2025-01-16

使用说明:
1. 双击运行此文件启动图形界面
2. 或在命令行中运行: python 启动通用优化工具.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import win32com.client
    except ImportError:
        missing_packages.append("pywin32")
    
    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")
    
    if missing_packages:
        error_msg = f"缺少以下依赖包: {', '.join(missing_packages)}\n\n"
        error_msg += "请运行以下命令安装:\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖包缺失", error_msg)
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("通用媒体预算优化工具 v1.0")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖包检查失败，程序退出")
        input("按回车键退出...")
        return
    
    # 检查必要文件
    required_files = [
        "universal_media_optimizer.py",
        "universal_optimizer_engine.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        error_msg = f"缺少以下必要文件: {', '.join(missing_files)}"
        print(f"错误: {error_msg}")
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动主程序
        from universal_media_optimizer import UniversalMediaOptimizer
        
        print("正在启动图形界面...")
        root = tk.Tk()
        app = UniversalMediaOptimizer(root)
        
        print("界面已启动，请在窗口中进行操作")
        root.mainloop()
        
    except Exception as e:
        error_msg = f"启动失败: {str(e)}"
        print(f"错误: {error_msg}")
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", error_msg)
        
        input("按回车键退出...")

if __name__ == "__main__":
    main()
