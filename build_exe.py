#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 - EXE打包脚本
使用PyInstaller将Python程序打包成独立的.exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['universal_media_optimizer_v2.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.py', '.'),
        ('*.xlsx', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'pandas',
        'numpy',
        'random',
        'threading',
        'time',
        'datetime',
        'json',
        'os',
        'sys',
        'traceback'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='媒体预算优化工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('media_optimizer.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建PyInstaller配置文件: media_optimizer.spec")

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'tkinter',
        'openpyxl', 
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'openpyxl':
                import openpyxl
            elif package == 'pandas':
                import pandas
            elif package == 'numpy':
                import numpy
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的依赖包: {', '.join(missing_packages)}")
        try:
            for package in missing_packages:
                if package != 'tkinter':  # tkinter通常随Python一起安装
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False
    
    return True

def build_exe():
    """构建EXE文件"""
    print("\n🔨 开始构建EXE文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "media_optimizer.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ EXE文件构建成功")
            
            # 检查生成的文件
            exe_path = Path("dist/媒体预算优化工具.exe")
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 EXE文件位置: {exe_path.absolute()}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到生成的EXE文件")
                return False
        else:
            print("❌ EXE文件构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

def create_icon():
    """创建应用图标（可选）"""
    # 这里可以添加创建图标的逻辑
    # 或者提示用户放置icon.ico文件
    if not os.path.exists('icon.ico'):
        print("💡 提示: 您可以在项目目录下放置 'icon.ico' 文件作为应用图标")

def cleanup():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['media_optimizer.spec']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 删除临时目录: {dir_name}")
            except Exception as e:
                print(f"⚠️  删除 {dir_name} 失败: {str(e)}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✅ 删除临时文件: {file_name}")
            except Exception as e:
                print(f"⚠️  删除 {file_name} 失败: {str(e)}")

def create_readme():
    """创建使用说明"""
    readme_content = """# 媒体预算优化工具 - 使用说明

## 📁 文件说明
- `媒体预算优化工具.exe` - 主程序，双击运行
- `Gatorade simulation tool_cal.xlsx` - 示例Excel文件（如果存在）

## 🚀 使用方法
1. 双击 `媒体预算优化工具.exe` 启动程序
2. 在界面中配置Excel文件路径和参数
3. 点击"开始优化"按钮运行优化
4. 查看生成的优化结果

## ⚙️ 系统要求
- Windows 7/8/10/11 (64位)
- 无需安装Python环境
- 建议内存: 4GB以上

## 📊 功能特点
- 智能媒体预算优化
- 多种差异化投放策略
- 递进式方案生成
- Excel数据自动处理
- 实时优化进度显示

## 🔧 故障排除
如果程序无法启动：
1. 确保Excel文件格式正确
2. 检查文件路径中是否包含中文字符
3. 尝试以管理员身份运行
4. 确保系统已安装Microsoft Visual C++ Redistributable

## 📞 技术支持
如有问题，请联系开发团队。
"""
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建使用说明文件")

def main():
    """主函数"""
    print("🚀 媒体预算优化工具 - EXE打包程序")
    print("=" * 60)
    
    # 检查主程序文件
    if not os.path.exists('universal_media_optimizer_v2.py'):
        print("❌ 未找到主程序文件: universal_media_optimizer_v2.py")
        return False
    
    # 步骤1: 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 步骤2: 检查依赖包
    if not check_dependencies():
        return False
    
    # 步骤3: 创建图标（可选）
    create_icon()
    
    # 步骤4: 创建配置文件
    create_spec_file()
    
    # 步骤5: 构建EXE
    if not build_exe():
        return False
    
    # 步骤6: 创建使用说明
    if os.path.exists('dist'):
        create_readme()
        
        # 复制Excel示例文件（如果存在）
        excel_files = ['Gatorade simulation tool_cal.xlsx']
        for excel_file in excel_files:
            if os.path.exists(excel_file):
                try:
                    shutil.copy2(excel_file, 'dist/')
                    print(f"✅ 复制示例文件: {excel_file}")
                except Exception as e:
                    print(f"⚠️  复制 {excel_file} 失败: {str(e)}")
    
    # 步骤7: 清理临时文件
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 EXE打包完成！")
    print(f"📁 输出目录: {os.path.abspath('dist')}")
    print("💡 您可以将dist目录中的文件分发给其他用户")
    print("⚠️  首次运行可能需要较长时间，请耐心等待")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 打包成功完成")
        else:
            print("\n❌ 打包过程中出现错误")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断打包过程")
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {str(e)}")
    
    input("\n按回车键退出...")
