#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用媒体预算优化工具 v2.0
"""

import os
from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

def test_optimizer():
    """测试优化器功能"""
    
    # 检查Excel文件是否存在
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return
    
    # 配置参数
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,  # C列
        "input_end_col": 18,   # R列
        "output_col": 21,      # U列
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 3,
        "baseline_allocation": "equal",  # 测试平均分配
        "custom_baseline_ratios": {},
        "channel_constraints": {
            # 测试一些自定义约束
            "SearchVolume": {"min": 0.05, "max": 0.50},
            "DouyinKOL": {"min": 0.02, "max": 0.30}
        }
    }
    
    # 模拟渠道列表（实际会从Excel读取）
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    print("=" * 60)
    print("测试通用媒体预算优化工具 v2.0")
    print("=" * 60)
    
    try:
        # 创建优化器实例
        optimizer = UniversalOptimizerEngineV2(config, channels)
        
        # 运行优化
        results = optimizer.optimize()
        
        print(f"\n✅ 测试成功完成！")
        print(f"生成了 {len(results['results'])} 个优化方案")
        print(f"基准KPI: {results['baseline_kpi']:,.2f}")
        
        if results['output_file']:
            print(f"结果已导出到: {results['output_file']}")
        
        # 显示前3个方案
        print(f"\n前3个优化方案:")
        for i, result in enumerate(results['results'][:3], 1):
            print(f"{i}. {result['方案名称']}: KPI = {result['KPI']:,.2f} ({result['提升比例']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_baseline():
    """测试自定义基准方案"""
    
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return
    
    # 配置自定义基准方案
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,
        "input_end_col": 18,
        "output_col": 21,
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 3,
        "baseline_allocation": "custom",  # 测试自定义基准
        "custom_baseline_ratios": {
            "SearchVolume": 20.0,
            "DouyinKOL": 15.0,
            "RedKOL": 10.0,
            "WeiboKOL": 8.0,
            "OTTPreroll": 12.0,
            "OTVPreroll": 8.0,
            "Display": 6.0,
            "DigitalCoop": 4.0,
            "RTBOCPX": 5.0,
            "BuildingLCD": 3.0,
            "Metro": 3.0,
            "CreativeOutdoor": 2.0,
            "SponsorEvent": 2.0,
            "SponsorDrama": 1.0,
            "DouyinPush": 1.0,
            "WeiboPush": 0.0  # 这个会被约束调整
        },
        "channel_constraints": {}
    }
    
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    print("\n" + "=" * 60)
    print("测试自定义基准方案功能")
    print("=" * 60)
    
    try:
        optimizer = UniversalOptimizerEngineV2(config, channels)
        results = optimizer.optimize()
        
        print(f"✅ 自定义基准测试成功！")
        print(f"基准KPI: {results['baseline_kpi']:,.2f}")
        
        # 显示基准方案的配比
        baseline_result = results['results'][0]
        print(f"\n自定义基准方案配比:")
        for channel, budget in baseline_result['预算分配'].items():
            ratio = budget / config['total_budget']
            print(f"  {channel}: {ratio:.1%} ({budget:,.0f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义基准测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试...")
    
    # 测试1: 基本功能
    success1 = test_optimizer()
    
    # 测试2: 自定义基准方案
    success2 = test_custom_baseline()
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过！工具已准备就绪。")
        print(f"\n使用方法:")
        print(f"1. 运行 'python 启动通用优化工具_v2.py' 启动图形界面")
        print(f"2. 或直接使用优化引擎进行编程调用")
    else:
        print(f"\n⚠️  部分测试失败，请检查配置和文件。")
    
    input("\n按回车键退出...")
