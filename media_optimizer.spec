# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['universal_media_optimizer_v2.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.py', '.'),
        ('*.xlsx', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'pandas',
        'numpy',
        'random',
        'threading',
        'time',
        'datetime',
        'json',
        'os',
        'sys',
        'traceback'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='媒体预算优化工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
