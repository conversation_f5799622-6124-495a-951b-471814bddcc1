import win32com.client
import os
import time

def test_excel_write():
    try:
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        excel_path = os.path.join(current_dir, "Gatorade simulation tool_cal.xlsx")
        
        print(f"测试Excel文件路径: {excel_path}")
        print(f"文件是否存在: {os.path.exists(excel_path)}")
        
        # 创建Excel应用程序实例
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = True  # 设置为可见，方便调试
        excel.DisplayAlerts = False
        
        try:
            # 打开工作簿
            print("正在打开工作簿...")
            workbook = excel.Workbooks.Open(excel_path)
            print("工作簿打开成功")
            
            # 获取工作表
            worksheet = workbook.Worksheets("calculation")
            print("获取工作表成功")
            
            # 测试写入单个单元格
            print("测试写入单元格...")
            worksheet.Cells(1, 1).Value = "测试数据"
            print("单元格写入成功")
            
            # 等待一下，确保写入完成
            time.sleep(2)
            
            # 保存并关闭
            print("正在保存工作簿...")
            workbook.Save()
            print("工作簿保存成功")
            
        except Exception as e:
            print(f"操作过程中出错: {str(e)}")
            print(f"错误类型: {type(e)}")
            print(f"错误详情: {e.args}")
        finally:
            # 关闭工作簿和Excel
            try:
                workbook.Close(SaveChanges=True)
                excel.Quit()
                print("Excel已关闭")
            except:
                pass
            
    except Exception as e:
        print(f"初始化Excel时出错: {str(e)}")
        print(f"错误类型: {type(e)}")
        print(f"错误详情: {e.args}")

if __name__ == "__main__":
    test_excel_write() 