# 通用媒体预算优化工具使用说明

## 📋 工具概述

这是一个通用的媒体预算优化工具，可以帮助您：
- 自动分析不同媒体渠道的效果
- 生成多种优化的预算分配方案
- 支持自定义约束条件
- 导出详细的分析报告

## 🚀 快速开始

### 1. 环境准备
确保您的系统已安装：
- Python 3.7+
- Microsoft Excel
- 必要的Python包：`pywin32`, `numpy`, `tkinter`

### 2. 启动工具
双击运行 `启动通用优化工具.py` 或在命令行中执行：
```bash
python 启动通用优化工具.py
```

## 🔧 配置说明

### 1. 文件设置
- **Excel文件**: 选择包含媒体数据的Excel文件
- **工作表名称**: 指定包含计算公式的工作表名（默认：calculation）

### 2. 数据区域设置
- **输入区域**: 指定预算输入的单元格范围
  - 行范围：如 38-57（20行）
  - 列范围：如 3-18（C列到R列，16个渠道）
- **输出列**: 指定KPI计算结果的列（如U列=21）
- **渠道名称行**: 指定渠道名称所在的行（如第1行）

### 3. 预算设置
- **总预算**: 设置总的媒体投放预算
- **优化方案数量**: 指定要生成的优化方案数量
- **基准方案**: 选择基准对比方案类型
  - `equal`: 平均分配
  - `custom`: 自定义分配

### 4. 渠道约束设置
- 点击"读取渠道信息"获取所有媒体渠道
- 点击"设置渠道约束"为每个渠道设置预算比例的上下限

## 📊 使用流程

### 步骤1: 配置基本信息
1. 选择Excel文件
2. 设置工作表名称和数据区域
3. 设置总预算和方案数量

### 步骤2: 读取渠道信息
1. 点击"读取渠道信息"按钮
2. 系统会自动从Excel中读取所有媒体渠道名称
3. 在文本框中查看读取到的渠道列表

### 步骤3: 设置约束条件（可选）
1. 点击"设置渠道约束"按钮
2. 在弹出窗口中为每个渠道设置预算比例的最小值和最大值
3. 例如：某渠道最小5%，最大30%

### 步骤4: 保存配置（可选）
1. 点击"保存配置"将当前设置保存为JSON文件
2. 下次使用时可以通过"加载配置"快速恢复设置

### 步骤5: 开始优化
1. 点击"开始优化"按钮
2. 系统会自动：
   - 测试每个渠道的单独表现
   - 生成多种优化方案
   - 计算每种方案的KPI提升
   - 导出详细的分析报告

## 📈 结果解读

### 优化报告包含：
1. **策略对比总览**: 各方案的KPI对比
2. **详细预算分配**: 每个渠道的具体预算比例
3. **原始输入数据**: 可直接复制到Excel验证的数据

### 关键指标：
- **KPI**: 各方案的关键绩效指标值
- **提升**: 相对于基准方案的绝对提升
- **提升比例**: 相对于基准方案的百分比提升

## 🎯 优化策略

工具会自动生成以下类型的方案：
1. **基准方案**: 平均分配或自定义基准
2. **高效渠道集中方案**: 重点投放表现最好的渠道
3. **渐进式优化方案**: 根据效果逐步调整预算
4. **智能搜索方案**: 通过算法搜索最优组合

## ⚙️ 高级功能

### 配置文件管理
- 支持保存和加载配置文件（JSON格式）
- 可以为不同项目创建不同的配置模板

### 约束条件设置
- 支持为每个渠道设置预算比例的上下限
- 确保优化结果符合实际投放限制

### 批量处理
- 可以通过修改配置文件实现批量优化
- 支持多个项目的自动化处理

## 🔍 故障排除

### 常见问题：

**Q: 提示"找不到Excel文件"**
A: 确保文件路径正确，且Excel文件未被其他程序占用

**Q: 读取渠道信息失败**
A: 检查工作表名称和渠道名称行设置是否正确

**Q: 优化过程中出错**
A: 确保Excel文件中的公式正常工作，数据区域设置正确

**Q: 结果不合理**
A: 检查渠道约束设置，确保总预算和区域设置正确

### 技术支持：
如遇到问题，请检查：
1. Python环境和依赖包是否正确安装
2. Excel文件格式和公式是否正常
3. 数据区域设置是否与实际文件匹配

## 📝 配置文件示例

```json
{
  "excel_file": "C:/path/to/your/file.xlsx",
  "worksheet_name": "calculation",
  "input_start_row": 38,
  "input_end_row": 57,
  "input_start_col": 3,
  "input_end_col": 18,
  "output_col": 21,
  "channel_names_row": 1,
  "total_budget": 1000000,
  "optimization_schemes": 5,
  "baseline_allocation": "equal",
  "channel_constraints": {
    "SearchVolume": {"min": 0.1, "max": 0.6},
    "DouyinKOL": {"min": 0.05, "max": 0.3}
  }
}
```

## 🎉 使用技巧

1. **首次使用**: 建议先用默认设置测试，确保工具正常工作
2. **约束设置**: 根据实际投放限制合理设置渠道约束
3. **方案对比**: 重点关注提升比例较高且风险可控的方案
4. **结果验证**: 使用导出的原始数据在Excel中验证结果
5. **配置保存**: 为常用项目保存配置模板，提高效率

---

**版本**: 1.0  
**更新日期**: 2025-01-16  
**作者**: AI Assistant
