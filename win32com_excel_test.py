import win32com.client
import os

EXCEL_PATH = os.path.abspath("Gatorade simulation tool_cal.xlsx")

try:
    excel = win32com.client.Dispatch("Excel.Application")
    excel.Visible = True  # 可见，便于观察
    print(f"打开文件: {EXCEL_PATH}")
    wb = excel.Workbooks.Open(EXCEL_PATH)
    print("工作表列表:")
    for i in range(1, wb.Sheets.Count+1):
        print(f"  {i}: {wb.Sheets(i).Name}")
    # 默认第一个sheet
    sheet = wb.Sheets(1)
    print(f"当前工作表: {sheet.Name}")
    print("尝试写入C38...")
    sheet.Cells(38, 3).Value = 12345
    print("写入成功，保存并关闭文件。")
    wb.Save()
    wb.Close()
    excel.Quit()
    print("测试完成，无异常。");
except Exception as e:
    print(f"发生异常: {e}")
    try:
        excel.Quit()
    except:
        pass 