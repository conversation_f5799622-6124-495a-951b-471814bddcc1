import pandas as pd
import numpy as np
import openpyxl
from openpyxl.utils import get_column_letter
import win32com.client
import os
import time
from openpyxl import load_workbook
from tqdm import tqdm
import traceback

def check_excel_file(file_path):
    """检查Excel文件的工作表信息"""
    print(f"\n检查Excel文件: {file_path}")
    try:
        wb = openpyxl.load_workbook(file_path, data_only=True)
        print("工作表列表:")
        for sheet in wb.sheetnames:
            print(f"- {sheet}")
        return True
    except Exception as e:
        print(f"读取Excel文件出错: {e}")
        return False

def read_media_names(file_path):
    """读取第1行C-R列媒体渠道名称"""
    wb = openpyxl.load_workbook(file_path, data_only=True)
    sheet = wb.active
    media_names = []
    for col in range(3, 19):  # C=3, R=18
        cell_value = sheet.cell(row=1, column=col).value
        if cell_value:
            media_names.append(str(cell_value).strip())
    return media_names

def generate_ratios(media_names):
    n = len(media_names)
    ratios_list = []
    # 1. 均分
    ratios_list.append(("均分", np.ones(n) / n))
    # 2. 搜索优先
    search_idx = [i for i, name in enumerate(media_names) if any(k in name.lower() for k in ["search", "sem", "seo", "百度", "google"])]
    arr = np.ones(n)
    if search_idx:
        arr[search_idx] = 3
    ratios_list.append(("搜索优先", arr / arr.sum()))
    # 3. 社交优先
    social_idx = [i for i, name in enumerate(media_names) if any(k in name.lower() for k in ["social", "微信", "微博", "qq", "社交", "朋友圈", "小红书", "抖音", "douyin"])]
    arr = np.ones(n)
    if social_idx:
        arr[social_idx] = 3
    ratios_list.append(("社交优先", arr / arr.sum()))
    # 4. 视频优先
    video_idx = [i for i, name in enumerate(media_names) if any(k in name.lower() for k in ["video", "视频", "ott", "otv"])]
    arr = np.ones(n)
    if video_idx:
        arr[video_idx] = 3
    ratios_list.append(("视频优先", arr / arr.sum()))
    # 5. 户外优先
    outdoor_idx = [i for i, name in enumerate(media_names) if any(k in name.lower() for k in ["outdoor", "户外", "lcd", "metro"])]
    arr = np.ones(n)
    if outdoor_idx:
        arr[outdoor_idx] = 3
    ratios_list.append(("户外优先", arr / arr.sum()))
    return ratios_list

def build_input_matrix(ratios, total_budget=1000000, n_rows=20):
    row_budget = total_budget / n_rows
    return np.tile(ratios * row_budget, (n_rows, 1))

def calculate_y_predict_from_excel(excel_file, input_matrix, media_names):
    excel = win32com.client.Dispatch("Excel.Application")
    excel.Visible = False
    try:
        wb = excel.Workbooks.Open(os.path.abspath(excel_file))
        sheet = wb.Sheets(1)
        for i, row in enumerate(input_matrix):
            for j, value in enumerate(row):
                try:
                    sheet.Cells(38 + i, 3 + j).Value = value
                except Exception as e:
                    print(f"写入单元格({38+i},{3+j})时出错: {e}")
                    continue
        excel.CalculateUntilAsyncQueriesDone()
        total_y = 0
        for i in range(38, 58):
            v = sheet.Cells(i, 21).Value
            if v is not None:
                total_y += float(v)
        wb.Close(SaveChanges=False)
        return total_y
    finally:
        excel.Quit()

def read_excel_with_openpyxl(file_path):
    """使用openpyxl读取Excel文件，读取第1行C到R列作为渠道名"""
    try:
        wb = load_workbook(file_path, data_only=True)
        ws = wb['calculation']
        # 读取第1行第3列到第18列（C到R）
        channels = []
        for col in range(3, 19):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value:
                channels.append(str(cell_value))
        return channels
    except Exception as e:
        print(f"使用openpyxl读取Excel时出错: {str(e)}")
        return None

def read_y_predict(excel_path, row):
    """读取指定行的Y_predict值"""
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        excel.DisplayAlerts = False
        try:
            workbook = excel.Workbooks.Open(excel_path)
            worksheet = workbook.Worksheets("calculation")
            # 假设Y_predict在S列（第19列）
            y_value = worksheet.Cells(row, 19).Value
            return float(y_value) if y_value is not None else 0
        finally:
            workbook.Close(SaveChanges=False)
            excel.Quit()
    except Exception as e:
        print(f"读取Y_predict出错: {str(e)}")
        return 0

def generate_budget_schemes(channels, total_budget):
    """生成不同的预算分配方案"""
    schemes = []
    
    # 1. 均分方案
    equal_budget = total_budget / len(channels)
    schemes.append({
        "name": "均分方案",
        "budgets": [equal_budget] * len(channels)
    })
    
    # 2. 搜索优先方案
    search_channels = ['Search', 'SearchBrand', 'SearchCompetitor']
    other_channels = [ch for ch in channels if ch not in search_channels]
    search_budget = total_budget * 0.6 / len(search_channels)
    other_budget = total_budget * 0.4 / len(other_channels)
    search_priority = []
    for channel in channels:
        if channel in search_channels:
            search_priority.append(search_budget)
        else:
            search_priority.append(other_budget)
    schemes.append({
        "name": "搜索优先方案",
        "budgets": search_priority
    })
    
    # 3. 数字媒体优先方案
    digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
    other_channels = [ch for ch in channels if ch not in digital_channels]
    digital_budget = total_budget * 0.7 / len(digital_channels)
    other_budget = total_budget * 0.3 / len(other_channels)
    digital_priority = []
    for channel in channels:
        if channel in digital_channels:
            digital_priority.append(digital_budget)
        else:
            digital_priority.append(other_budget)
    schemes.append({
        "name": "数字媒体优先方案",
        "budgets": digital_priority
    })
    
    # 4. KOL优先方案
    kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
    other_channels = [ch for ch in channels if ch not in kol_channels]
    kol_budget = total_budget * 0.5 / len(kol_channels)
    other_budget = total_budget * 0.5 / len(other_channels)
    kol_priority = []
    for channel in channels:
        if channel in kol_channels:
            kol_priority.append(kol_budget)
        else:
            kol_priority.append(other_budget)
    schemes.append({
        "name": "KOL优先方案",
        "budgets": kol_priority
    })
    
    return schemes

def write_and_read_y_predict(excel_path, channels, budgets, start_row, end_row, scheme_name, max_retries=3):
    """在同一个Excel实例中写入预算并读取Y_predict，添加重试机制"""
    for attempt in range(max_retries):
        try:
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False
            try:
                workbook = excel.Workbooks.Open(excel_path)
                worksheet = workbook.Worksheets("calculation")
                
                # 写入预算到C38-R57
                for row in range(start_row, end_row + 1):
                    for i, budget in enumerate(budgets):
                        col = i + 3  # C=3
                        worksheet.Cells(row, col).Value = budget
                
                # 检查总预算
                total_budget = sum(budgets)
                if abs(total_budget - 1000000) > 0.01:
                    print(f"警告：总预算 {total_budget:,.2f} 不等于1,000,000")
                
                time.sleep(0.5)  # 减少等待时间
                workbook.Save()
                
                # 强制Excel重新计算
                worksheet.Calculate()
                
                # 读取U38-U57的Y_predict值并计算总和
                total_y = 0
                for row in range(start_row, end_row + 1):
                    y_value = worksheet.Cells(row, 21).Value  # U=21
                    total_y += float(y_value) if y_value is not None else 0
                
                return total_y
                
            finally:
                try:
                    workbook.Close(SaveChanges=True)
                    excel.Quit()
                except:
                    pass
                # 强制杀掉所有Excel进程
                try:
                    os.system("taskkill /f /im excel.exe >nul 2>&1")
                except:
                    pass
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"\n尝试 {attempt + 1} 失败，正在重试...")
                time.sleep(1)  # 重试前等待
            else:
                print(f"\n写入或读取Y_predict时出错: {str(e)}")
                raise

def calculate_average_allocation(excel_path, channels, total_budget, start_row, end_row):
    """计算平均分配的Y_predict作为基准"""
    # 计算每个渠道的平均预算
    avg_budget = total_budget / len(channels)
    budgets = [avg_budget] * len(channels)
    
    # 写入Excel并读取Y_predict
    y_value = write_and_read_y_predict(excel_path, channels, budgets, start_row, end_row, "平均分配")
    print(f"\n平均分配方案:")
    print(f"每个渠道预算: {avg_budget:,.2f}")
    print(f"Y_predict: {y_value:,.2f}")
    return y_value, budgets

def optimize_budget_allocation(excel_path, channels, total_budget, start_row, end_row):
    """通过迭代优化找出最优配比，优化计算效率"""
    # 先计算平均分配的基准值
    print("\n计算平均分配方案...")
    avg_budget = total_budget / len(channels)
    avg_budgets = [avg_budget] * len(channels)
    base_y = write_and_read_y_predict(excel_path, channels, avg_budgets, start_row, end_row, "平均分配")
    print(f"平均分配Y_predict: {base_y:,.2f}")
    
    best_y = base_y
    best_budgets = avg_budgets
    best_ratio = 1.0 / len(channels)
    
    # 记录所有尝试过的方案
    tried_schemes = {
        "平均分配": {
            "budgets": avg_budgets,
            "y_predict": base_y
        }
    }
    
    # 定义迭代参数
    max_iterations = 50  # 减少迭代次数
    step_size = 0.1     # 增大步长
    min_ratio = 0.01
    max_ratio = 0.5
    
    print("\n开始迭代优化...")
    for iteration in range(max_iterations):
        improved = False
        print(f"\r迭代进度: {iteration + 1}/{max_iterations}", end="")
        
        # 对每个渠道进行优化
        for i in range(len(channels)):
            # 尝试增加当前渠道的预算
            current_budgets = best_budgets.copy()
            current_budgets[i] += total_budget * step_size
            
            # 相应减少其他渠道的预算
            reduction = total_budget * step_size / (len(channels) - 1)
            for j in range(len(channels)):
                if j != i:
                    current_budgets[j] -= reduction
            
            # 确保预算不为负
            if min(current_budgets) < 0:
                continue
            
            # 计算新的Y_predict
            y_value = write_and_read_y_predict(excel_path, channels, current_budgets, start_row, end_row, f"迭代 {iteration+1}")
            
            # 记录这个方案
            scheme_key = f"迭代_{iteration+1}_渠道_{channels[i]}"
            tried_schemes[scheme_key] = {
                "budgets": current_budgets,
                "y_predict": y_value
            }
            
            # 如果Y_predict提高，更新最优解
            if y_value > best_y:
                best_y = y_value
                best_budgets = current_budgets
                best_ratio = best_budgets[i] / total_budget
                improved = True
                print(f"\n发现更好的结果！")
                print(f"渠道: {channels[i]}")
                print(f"预算比率: {best_ratio:.1%}")
                print(f"Y_predict: {y_value:,.2f}")
        
        # 如果没有改进，减小步长继续尝试
        if not improved:
            step_size *= 0.5
            if step_size < 0.0001:  # 如果步长太小，停止迭代
                break
    
    print("\n优化完成！")
    print(f"最优Y_predict: {best_y:,.2f}")
    print("\n最优预算分配:")
    for channel, budget in zip(channels, best_budgets):
        ratio = budget / total_budget
        print(f"{channel}: {ratio:.1%} ({budget:,.2f})")
    
    return best_y, best_ratio, best_budgets, tried_schemes

def write_results_to_excel(excel_path, channels, results):
    """将多个方案的结果写入Excel进行对比分析"""
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        excel.DisplayAlerts = False
        try:
            workbook = excel.Workbooks.Open(excel_path)
            
            # 检查是否存在"方案对比"sheet，如果不存在则创建
            try:
                worksheet = workbook.Worksheets("方案对比")
            except:
                worksheet = workbook.Worksheets.Add()
                worksheet.Name = "方案对比"
            
            # 写入表头
            worksheet.Cells(1, 1).Value = "方案名称"
            worksheet.Cells(1, 2).Value = "Y_predict"
            for i, channel in enumerate(channels):
                worksheet.Cells(1, i + 3).Value = channel
            
            # 写入每个方案的结果
            for i, result in enumerate(results):
                row = i + 2
                worksheet.Cells(row, 1).Value = result["方案名称"]
                worksheet.Cells(row, 2).Value = result["Y_predict"]
                for j, channel in enumerate(channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / 1000000
                    worksheet.Cells(row, j + 3).Value = ratio
                    worksheet.Cells(row, j + 3).NumberFormat = "0.00%"
            
            # 设置列宽
            worksheet.Columns("A").ColumnWidth = 20
            worksheet.Columns("B").ColumnWidth = 15
            for i in range(3, len(channels) + 3):
                worksheet.Columns(i).ColumnWidth = 12
            
            # 添加条件格式
            y_range = worksheet.Range(f"B2:B{len(results)+1}")
            y_range.FormatConditions.AddTop10()
            y_range.FormatConditions(1).TopBottom = 1
            y_range.FormatConditions(1).Rank = 1
            y_range.FormatConditions(1).Interior.Color = 5287936  # 绿色
            
            workbook.Save()
            print(f"\n方案对比结果已写入Excel的'方案对比'sheet")
            
        finally:
            workbook.Close(SaveChanges=True)
            excel.Quit()
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass
    except Exception as e:
        print(f"\n写入Excel时出错: {str(e)}")
        raise

def generate_alternative_schemes(excel_path, channels, total_budget, start_row, end_row):
    """生成多个不同的投放方案，优化计算效率"""
    results = []
    
    # 1. 最优方案（通过迭代优化找到的）
    print("\n1. 计算最优方案...")
    best_y, best_ratio, best_budgets, tried_schemes = optimize_budget_allocation(
        excel_path, channels, total_budget, start_row, end_row
    )
    results.append({
        "方案名称": "最优方案",
        "Y_predict": best_y,
        "预算分配": dict(zip(channels, best_budgets))
    })
    print("最优方案计算完成。")
    
    # 2. 平均分配方案（从已尝试的方案中获取）
    print("\n2. 使用平均分配方案...")
    avg_scheme = tried_schemes["平均分配"]
    results.append({
        "方案名称": "平均分配方案",
        "Y_predict": avg_scheme["y_predict"],
        "预算分配": dict(zip(channels, avg_scheme["budgets"]))
    })
    print("平均分配方案计算完成。")
    
    # 3. 数字媒体优先方案
    print("\n3. 计算数字媒体优先方案...")
    digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
    digital_budgets = []
    for channel in channels:
        if channel in digital_channels:
            digital_budgets.append(total_budget * 0.7 / len(digital_channels))
        else:
            digital_budgets.append(total_budget * 0.3 / (len(channels) - len(digital_channels)))
    digital_y = write_and_read_y_predict(excel_path, channels, digital_budgets, start_row, end_row, "数字媒体优先")
    results.append({
        "方案名称": "数字媒体优先方案",
        "Y_predict": digital_y,
        "预算分配": dict(zip(channels, digital_budgets))
    })
    print("数字媒体优先方案计算完成。")
    
    # 4. KOL优先方案
    print("\n4. 计算KOL优先方案...")
    kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
    kol_budgets = []
    for channel in channels:
        if channel in kol_channels:
            kol_budgets.append(total_budget * 0.6 / len(kol_channels))
        else:
            kol_budgets.append(total_budget * 0.4 / (len(channels) - len(kol_channels)))
    kol_y = write_and_read_y_predict(excel_path, channels, kol_budgets, start_row, end_row, "KOL优先")
    results.append({
        "方案名称": "KOL优先方案",
        "Y_predict": kol_y,
        "预算分配": dict(zip(channels, kol_budgets))
    })
    print("KOL优先方案计算完成。")
    
    # 5. 搜索优先方案
    print("\n5. 计算搜索优先方案...")
    search_channels = ['SearchVolume']
    search_budgets = []
    for channel in channels:
        if channel in search_channels:
            search_budgets.append(total_budget * 0.4 / len(search_channels))
        else:
            search_budgets.append(total_budget * 0.6 / (len(channels) - len(search_channels)))
    search_y = write_and_read_y_predict(excel_path, channels, search_budgets, start_row, end_row, "搜索优先")
    results.append({
        "方案名称": "搜索优先方案",
        "Y_predict": search_y,
        "预算分配": dict(zip(channels, search_budgets))
    })
    print("搜索优先方案计算完成。")
    
    # 将结果写入Excel
    write_results_to_excel(excel_path, channels, results)
    
    return results

def test_excel_write(excel_path):
    """测试Excel写入功能"""
    print("\n开始测试Excel写入功能...")
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        excel.DisplayAlerts = False
        try:
            print("1. 尝试打开Excel文件...")
            workbook = excel.Workbooks.Open(excel_path)
            worksheet = workbook.Worksheets("方案对比")
            
            print("2. 尝试写入测试数据...")
            worksheet.Cells(1, 1).Value = "测试数据"
            worksheet.Cells(1, 2).Value = "测试值"
            
            print("3. 尝试保存文件...")
            workbook.Save()
            print("文件保存成功！")
            
            print("4. 尝试关闭文件...")
            workbook.Close(SaveChanges=True)
            print("文件关闭成功！")
            
        except Exception as e:
            print(f"操作Excel时出错: {str(e)}")
            raise
        finally:
            print("5. 尝试退出Excel...")
            excel.Quit()
            print("Excel退出成功！")
            
            print("6. 尝试清理Excel进程...")
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
                print("Excel进程清理成功！")
            except:
                print("清理Excel进程时出错")
                
    except Exception as e:
        print(f"初始化Excel时出错: {str(e)}")
        raise

def test_output_to_new_excel(excel_path, channels, best_y, best_budgets):
    """测试将结果输出到新的Excel文件"""
    print("\n开始测试输出到新Excel文件...")
    try:
        # 创建新的Excel文件
        output_path = os.path.join(os.path.dirname(excel_path), "优化结果_" + os.path.basename(excel_path))
        print(f"1. 创建新文件: {output_path}")
        
        # 先检查文件是否存在，如果存在则删除
        if os.path.exists(output_path):
            print("发现已存在的文件，正在删除...")
            os.remove(output_path)
        
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        excel.DisplayAlerts = False
        try:
            # 创建新的工作簿
            print("2. 创建新工作簿...")
            workbook = excel.Workbooks.Add()
            
            # 添加方案对比sheet
            print("3. 准备写入数据...")
            worksheet = workbook.Worksheets(1)
            worksheet.Name = "方案对比"
            
            # 写入表头
            print("4. 写入表头...")
            worksheet.Cells(1, 1).Value = "渠道"
            worksheet.Cells(1, 2).Value = "预算"
            worksheet.Cells(1, 3).Value = "占比"
            worksheet.Cells(1, 4).Value = "Y_predict"
            
            # 写入数据
            print("5. 写入数据...")
            for i, (channel, budget) in enumerate(zip(channels, best_budgets), 2):
                worksheet.Cells(i, 1).Value = channel
                worksheet.Cells(i, 2).Value = budget
                worksheet.Cells(i, 3).Value = budget / 1000000
                worksheet.Cells(i, 4).Value = best_y
            
            # 保存并关闭
            print("6. 保存文件...")
            workbook.SaveAs(output_path)
            print("文件保存成功！")
            
            print("7. 关闭文件...")
            workbook.Close(SaveChanges=True)
            print("文件关闭成功！")
            
        except Exception as e:
            print(f"操作Excel时出错: {str(e)}")
            raise
        finally:
            print("8. 退出Excel...")
            excel.Quit()
            print("Excel退出成功！")
            
            print("9. 清理Excel进程...")
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
                print("Excel进程清理成功！")
            except:
                print("清理Excel进程时出错")
                
    except Exception as e:
        print(f"初始化Excel时出错: {str(e)}")
        raise

def main():
    """主函数"""
    try:
        # 设置Excel文件路径
        excel_path = os.path.abspath("Gatorade simulation tool_cal.xlsx")
        
        # 检查Excel文件
        if not check_excel_file(excel_path):
            return
            
        # 测试Excel写入功能
        test_excel_write(excel_path)
        
        # 读取媒体渠道名称
        channels = read_media_names(excel_path)
        if not channels:
            print("未找到媒体渠道名称")
            return
        
        print("\n找到的媒体渠道:")
        for i, channel in enumerate(channels, 1):
            print(f"{i}. {channel}")
        
        # 设置参数
        total_budget = 1000000  # 总预算100万
        start_row = 38  # 开始行
        end_row = 57    # 结束行
        
        # 计算最优方案
        print("\n开始计算最优方案...")
        best_y, best_ratio, best_budgets, tried_schemes = optimize_budget_allocation(
            excel_path, channels, total_budget, start_row, end_row
        )
        
        # 测试输出到新Excel文件
        test_output_to_new_excel(excel_path, channels, best_y, best_budgets)
        
        # 输出分析报告
        print("\n=== 最优方案分析报告 ===")
        print(f"\n总Y_predict: {best_y:,.2f}")
        print("\n预算分配详情:")
        
        # 按预算比例从高到低排序
        budget_allocations = [(channel, budget) for channel, budget in zip(channels, best_budgets)]
        budget_allocations.sort(key=lambda x: x[1], reverse=True)
        
        # 输出主要投放渠道（预算比例>5%）
        print("\n主要投放渠道:")
        for channel, budget in budget_allocations:
            ratio = budget / total_budget
            if ratio > 0.05:
                print(f"{channel}: {ratio:.1%} ({budget:,.2f})")
        
        # 输出次要投放渠道（预算比例<=5%）
        print("\n次要投放渠道:")
        for channel, budget in budget_allocations:
            ratio = budget / total_budget
            if ratio <= 0.05:
                print(f"{channel}: {ratio:.1%} ({budget:,.2f})")
        
        # 计算渠道类型分布
        digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
        kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
        outdoor_channels = ['BuildingLCD', 'Metro', 'CreativeOutdoor']
        sponsor_channels = ['SponsorEvent', 'SponsorDrama']
        
        digital_budget = sum(best_budgets[i] for i, ch in enumerate(channels) if ch in digital_channels)
        kol_budget = sum(best_budgets[i] for i, ch in enumerate(channels) if ch in kol_channels)
        outdoor_budget = sum(best_budgets[i] for i, ch in enumerate(channels) if ch in outdoor_channels)
        sponsor_budget = sum(best_budgets[i] for i, ch in enumerate(channels) if ch in sponsor_channels)
        
        print("\n渠道类型分布:")
        print(f"数字媒体: {digital_budget/total_budget:.1%} ({digital_budget:,.2f})")
        print(f"KOL: {kol_budget/total_budget:.1%} ({kol_budget:,.2f})")
        print(f"户外媒体: {outdoor_budget/total_budget:.1%} ({outdoor_budget:,.2f})")
        print(f"赞助活动: {sponsor_budget/total_budget:.1%} ({sponsor_budget:,.2f})")
        
        # 输出优化建议
        print("\n优化建议:")
        if best_y > 20000:
            print("1. 当前方案表现优秀，建议保持主要投放渠道的预算比例")
        else:
            print("1. 建议增加数字媒体和KOL的投放比例")
        
        if kol_budget/total_budget < 0.3:
            print("2. 考虑增加KOL投放预算，特别是表现较好的平台")
        
        if digital_budget/total_budget < 0.4:
            print("3. 建议提高数字媒体投放比例，特别是搜索和视频广告")
        
        if outdoor_budget/total_budget > 0.2:
            print("4. 户外媒体投放比例较高，建议适当降低并转移至数字媒体")
        
        if sponsor_budget/total_budget > 0.15:
            print("5. 赞助活动预算占比较高，建议评估其ROI并适当调整")
        
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 