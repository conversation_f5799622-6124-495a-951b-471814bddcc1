#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试差异化方案生成
1. 基于渠道类别的差异化策略
2. 避免方案同质化
3. 不同投放策略的预算分配
4. 递进提升趋势保持
"""

import os
from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

def test_differentiated_strategy_generation():
    """测试差异化策略生成"""
    print("🧪 测试差异化策略生成")
    print("=" * 50)
    
    # 使用多样化的渠道组合
    channels = [
        # 数字媒体
        "SearchVolume", "Display", "RTBOCPX", "DigitalCoop",
        # 社交媒体  
        "DouyinKOL", "RedKOL", "WeiboKOL", "DouyinPush",
        # 视频广告
        "OTTPreroll", "OTVPreroll",
        # 线下媒体
        "BuildingLCD", "Metro", "CreativeOutdoor",
        # 赞助营销
        "SponsorEvent", "SponsorDrama"
    ]
    
    config = {
        "total_budget": 1000000,
        "excel_file": "dummy.xlsx", "worksheet_name": "test",
        "input_start_row": 1, "input_end_row": 10,
        "input_start_col": 1, "input_end_col": 10,
        "output_col": 11, "output_start_row": 1, "output_end_row": 10,
        "channel_names_row": 1, "optimization_schemes": 5, "max_iterations": 100,
        "baseline_allocation": "equal", "custom_baseline_ratios": {}, "channel_constraints": {}
    }
    
    optimizer = UniversalOptimizerEngineV2(config, channels)
    
    # 模拟渠道表现数据
    channel_performance = [
        {'channel': 'SearchVolume', 'improvement': 1200, 'efficiency': 0.6},
        {'channel': 'DouyinKOL', 'improvement': 1000, 'efficiency': 0.5},
        {'channel': 'RedKOL', 'improvement': 900, 'efficiency': 0.45},
        {'channel': 'Display', 'improvement': 800, 'efficiency': 0.4},
        {'channel': 'OTTPreroll', 'improvement': 700, 'efficiency': 0.35},
        {'channel': 'WeiboKOL', 'improvement': 600, 'efficiency': 0.3},
        {'channel': 'RTBOCPX', 'improvement': 500, 'efficiency': 0.25},
        {'channel': 'BuildingLCD', 'improvement': 400, 'efficiency': 0.2},
        {'channel': 'SponsorEvent', 'improvement': 300, 'efficiency': 0.15},
        {'channel': 'OTVPreroll', 'improvement': 200, 'efficiency': 0.1},
        {'channel': 'Metro', 'improvement': 150, 'efficiency': 0.08},
        {'channel': 'DouyinPush', 'improvement': 100, 'efficiency': 0.05},
        {'channel': 'CreativeOutdoor', 'improvement': 50, 'efficiency': 0.03},
        {'channel': 'SponsorDrama', 'improvement': 20, 'efficiency': 0.01},
        {'channel': 'DigitalCoop', 'improvement': -50, 'efficiency': -0.02}
    ]
    
    baseline_kpi = 10000
    
    # 生成差异化方案
    differentiated_schemes = optimizer.generate_differentiated_schemes(baseline_kpi, channel_performance)
    
    print("差异化方案生成结果:")
    print("-" * 60)
    
    if not differentiated_schemes:
        print("⚠️  未生成任何差异化方案")
        return False
    
    # 按提升幅度排序显示
    differentiated_schemes.sort(key=lambda x: float(x["提升比例"].rstrip('%')))
    
    for i, scheme in enumerate(differentiated_schemes, 1):
        improvement_pct = float(scheme["提升比例"].rstrip('%'))
        
        print(f"\n📊 {i}. {scheme['方案名称']} (提升: {scheme['提升比例']})")
        print(f"   策略重点: {', '.join(scheme.get('策略类型', []))}")
        print(f"   描述: {scheme['方案描述']}")
        
        # 分析渠道类别分配
        channel_classification, _ = optimizer.get_channel_categories()
        category_budgets = {}
        
        for category, channels_in_cat in channel_classification.items():
            total_budget_in_cat = sum(scheme['预算分配'].get(ch, 0) for ch in channels_in_cat)
            ratio = total_budget_in_cat / config["total_budget"]
            if ratio > 0.05:  # 只显示占比超过5%的类别
                category_budgets[category] = ratio
        
        print(f"   类别分配:")
        for category, ratio in sorted(category_budgets.items(), key=lambda x: x[1], reverse=True):
            print(f"     {category}: {ratio:.1%}")
        
        # 显示主要渠道
        top_channels = sorted(scheme['预算分配'].items(), key=lambda x: x[1], reverse=True)[:3]
        print(f"   主要渠道:")
        for channel, budget in top_channels:
            ratio = budget / config["total_budget"]
            print(f"     {channel}: {ratio:.1%}")
    
    # 验证差异化程度
    print(f"\n📈 差异化分析:")
    strategy_types = [set(s.get('策略类型', [])) for s in differentiated_schemes]
    unique_strategies = len(set(frozenset(st) for st in strategy_types))
    print(f"   独特策略类型: {unique_strategies}/{len(differentiated_schemes)}")
    
    # 验证递进趋势
    improvements = [float(s["提升比例"].rstrip('%')) for s in differentiated_schemes]
    is_progressive = all(improvements[i] <= improvements[i+1] for i in range(len(improvements)-1))
    print(f"   递进趋势: {'✅ 正确' if is_progressive else '❌ 错误'}")
    print(f"   提升范围: {min(improvements):.1f}% - {max(improvements):.1f}%")
    
    print(f"\n✅ 差异化策略生成测试完成")
    return True

def test_channel_category_allocation():
    """测试渠道类别分配策略"""
    print(f"\n🧪 测试渠道类别分配策略")
    print("=" * 50)
    
    channels = ["SearchVolume", "DouyinKOL", "RedKOL", "OTTPreroll", "BuildingLCD", "SponsorEvent"]
    
    config = {
        "total_budget": 1000000,
        "excel_file": "dummy.xlsx", "worksheet_name": "test",
        "input_start_row": 1, "input_end_row": 10,
        "input_start_col": 1, "input_end_col": 10,
        "output_col": 11, "output_start_row": 1, "output_end_row": 10,
        "channel_names_row": 1, "optimization_schemes": 3, "max_iterations": 100,
        "baseline_allocation": "equal", "custom_baseline_ratios": {}, "channel_constraints": {}
    }
    
    optimizer = UniversalOptimizerEngineV2(config, channels)
    
    # 获取渠道分类
    channel_classification, _ = optimizer.get_channel_categories()
    
    print("渠道分类结果:")
    for category, channels_in_cat in channel_classification.items():
        if channels_in_cat:
            print(f"  {category}: {', '.join(channels_in_cat)}")
    
    # 测试不同策略的预算分配
    strategies = [
        {
            "name": "数字营销主导",
            "focus_categories": ["数字媒体", "社交媒体"],
            "focus_ratio": 0.7
        },
        {
            "name": "品牌建设主导",
            "focus_categories": ["视频广告", "线下媒体", "赞助营销"],
            "focus_ratio": 0.65
        },
        {
            "name": "社交互动主导",
            "focus_categories": ["社交媒体"],
            "focus_ratio": 0.6
        }
    ]
    
    channel_performance = [
        {'channel': 'SearchVolume', 'improvement': 1000, 'efficiency': 0.5},
        {'channel': 'DouyinKOL', 'improvement': 800, 'efficiency': 0.4},
        {'channel': 'RedKOL', 'improvement': 600, 'efficiency': 0.3},
        {'channel': 'OTTPreroll', 'improvement': 400, 'efficiency': 0.2},
        {'channel': 'BuildingLCD', 'improvement': 200, 'efficiency': 0.1},
        {'channel': 'SponsorEvent', 'improvement': 100, 'efficiency': 0.05}
    ]
    
    print(f"\n策略分配测试:")
    print("-" * 40)
    
    for strategy in strategies:
        print(f"\n📊 {strategy['name']}策略 (重点比例: {strategy['focus_ratio']:.0%})")
        
        budgets = optimizer.allocate_budget_by_strategy(strategy, channel_classification, channel_performance)
        
        # 显示分配结果
        for i, (channel, budget) in enumerate(zip(channels, budgets)):
            ratio = budget / config["total_budget"]
            print(f"   {channel:<15}: {ratio:>6.1%} (¥{budget:>8,.0f})")
        
        # 计算类别集中度
        focus_budget = 0
        other_budget = 0
        
        for channel, budget in zip(channels, budgets):
            is_focus = False
            for category, channels_in_cat in channel_classification.items():
                if channel in channels_in_cat and category in strategy["focus_categories"]:
                    focus_budget += budget
                    is_focus = True
                    break
            if not is_focus:
                other_budget += budget
        
        actual_focus_ratio = focus_budget / config["total_budget"]
        print(f"   实际重点比例: {actual_focus_ratio:.1%} (目标: {strategy['focus_ratio']:.0%})")
    
    print(f"\n✅ 渠道类别分配测试完成")
    return True

def test_scheme_differentiation_quality():
    """测试方案差异化质量"""
    print(f"\n🧪 测试方案差异化质量")
    print("=" * 50)
    
    # 模拟生成的方案
    mock_schemes = [
        {
            "name": "数字营销主导方案",
            "categories": {"数字媒体": 0.4, "社交媒体": 0.3, "其他": 0.3},
            "top_channels": ["SearchVolume", "DouyinKOL", "Display"]
        },
        {
            "name": "品牌建设主导方案", 
            "categories": {"视频广告": 0.35, "线下媒体": 0.2, "赞助营销": 0.1, "其他": 0.35},
            "top_channels": ["OTTPreroll", "BuildingLCD", "SponsorEvent"]
        },
        {
            "name": "社交互动主导方案",
            "categories": {"社交媒体": 0.6, "其他": 0.4},
            "top_channels": ["DouyinKOL", "RedKOL", "WeiboKOL"]
        },
        {
            "name": "全渠道均衡方案",
            "categories": {"数字媒体": 0.25, "社交媒体": 0.25, "视频广告": 0.2, "线下媒体": 0.15, "其他": 0.15},
            "top_channels": ["SearchVolume", "DouyinKOL", "OTTPreroll"]
        }
    ]
    
    print("方案差异化质量分析:")
    print("-" * 50)
    
    # 分析类别重点差异
    category_focuses = {}
    for scheme in mock_schemes:
        main_category = max(scheme["categories"].items(), key=lambda x: x[1])
        category_focuses[scheme["name"]] = main_category
    
    print("主要类别重点:")
    for scheme_name, (category, ratio) in category_focuses.items():
        print(f"  {scheme_name}: {category} ({ratio:.0%})")
    
    # 分析渠道重叠度
    all_top_channels = [set(s["top_channels"]) for s in mock_schemes]
    overlaps = []
    
    for i in range(len(all_top_channels)):
        for j in range(i+1, len(all_top_channels)):
            overlap = len(all_top_channels[i] & all_top_channels[j])
            total_unique = len(all_top_channels[i] | all_top_channels[j])
            overlap_ratio = overlap / total_unique if total_unique > 0 else 0
            overlaps.append(overlap_ratio)
    
    avg_overlap = sum(overlaps) / len(overlaps) if overlaps else 0
    
    print(f"\n差异化指标:")
    print(f"  独特主要类别: {len(set(cat for _, (cat, _) in category_focuses.items()))}/{len(mock_schemes)}")
    print(f"  平均渠道重叠度: {avg_overlap:.1%}")
    print(f"  差异化质量: {'优秀' if avg_overlap < 0.3 else '良好' if avg_overlap < 0.5 else '需改进'}")
    
    # 验证策略逻辑
    strategy_logic_check = {
        "数字营销主导": ["数字媒体", "社交媒体"],
        "品牌建设主导": ["视频广告", "线下媒体", "赞助营销"],
        "社交互动主导": ["社交媒体"],
        "全渠道均衡": []  # 无特定重点
    }
    
    print(f"\n策略逻辑验证:")
    for scheme in mock_schemes:
        scheme_type = scheme["name"].replace("方案", "")
        expected_categories = strategy_logic_check.get(scheme_type, [])
        
        if expected_categories:
            focus_ratio = sum(scheme["categories"].get(cat, 0) for cat in expected_categories)
            is_valid = focus_ratio > 0.5  # 重点类别应占50%以上
            status = "✅" if is_valid else "❌"
            print(f"  {status} {scheme['name']}: 重点类别占比 {focus_ratio:.0%}")
        else:
            # 均衡方案检查
            ratios = list(scheme["categories"].values())
            max_ratio = max(ratios)
            is_balanced = max_ratio < 0.4  # 没有类别超过40%
            status = "✅" if is_balanced else "❌"
            print(f"  {status} {scheme['name']}: 最高类别占比 {max_ratio:.0%}")
    
    print(f"\n✅ 方案差异化质量测试完成")
    return True

def test_real_differentiated_optimization():
    """测试真实环境下的差异化优化"""
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"\n⚠️  跳过真实差异化优化测试：找不到Excel文件 {excel_file}")
        return True
    
    print(f"\n🧪 测试真实环境下的差异化优化")
    print("=" * 50)
    
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38, "input_end_row": 57,
        "input_start_col": 3, "input_end_col": 18,
        "output_col": 21, "output_start_row": 38, "output_end_row": 57,
        "channel_names_row": 1, "total_budget": 1000000,
        "optimization_schemes": 4, "max_iterations": 30,  # 减少迭代以加快测试
        "baseline_allocation": "equal", "custom_baseline_ratios": {}, "channel_constraints": {}
    }
    
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    try:
        print(f"📊 开始真实差异化优化测试...")
        
        def progress_callback(progress, status, detail=""):
            if progress % 50 == 0 or progress >= 95:
                print(f"  进度: {progress:3.0f}% | {status}")
        
        optimizer = UniversalOptimizerEngineV2(config, channels, progress_callback)
        results = optimizer.optimize()
        
        print(f"\n📋 差异化优化结果分析:")
        print("-" * 50)
        
        schemes = results['results']
        optimization_schemes = [s for s in schemes if "基准方案" not in s['方案名称']]
        
        print(f"生成方案: {len(schemes)} 个")
        print(f"优化方案: {len(optimization_schemes)} 个")
        
        if optimization_schemes:
            print(f"\n📊 方案差异化分析:")
            
            # 分析渠道类别分布差异
            channel_classification, _ = optimizer.get_channel_categories()
            
            for i, scheme in enumerate(optimization_schemes, 1):
                print(f"\n{i}. {scheme['方案名称']} ({scheme['提升比例']})")
                
                # 计算类别分配
                category_ratios = {}
                for category, channels_in_cat in channel_classification.items():
                    total_budget_in_cat = sum(scheme['预算分配'].get(ch, 0) for ch in channels_in_cat)
                    ratio = total_budget_in_cat / config['total_budget']
                    if ratio > 0.1:  # 只显示占比超过10%的类别
                        category_ratios[category] = ratio
                
                print(f"   主要类别: {', '.join([f'{cat}({ratio:.0%})' for cat, ratio in sorted(category_ratios.items(), key=lambda x: x[1], reverse=True)[:2]])}")
        
        print(f"\n✅ 真实差异化优化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 真实差异化优化测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试差异化方案生成系统")
    print("=" * 60)
    
    tests = [
        ("差异化策略生成", test_differentiated_strategy_generation),
        ("渠道类别分配", test_channel_category_allocation),
        ("方案差异化质量", test_scheme_differentiation_quality),
        ("真实差异化优化", test_real_differentiated_optimization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 允许1个测试失败（Excel文件问题）
        print(f"🎉 差异化方案生成系统验证成功！")
        print(f"\n✨ 差异化特性:")
        print(f"   ✅ 基于渠道类别的差异化策略")
        print(f"   ✅ 数字营销 vs 品牌建设 vs 社交互动 vs 全渠道均衡")
        print(f"   ✅ 避免方案同质化")
        print(f"   ✅ 保持递进提升趋势")
        print(f"   ✅ 策略逻辑清晰验证")
    else:
        print(f"⚠️  部分测试失败，请检查相关功能")
    
    input(f"\n按回车键退出...")
