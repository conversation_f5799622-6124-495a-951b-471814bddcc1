import pandas as pd
import numpy as np
import openpyxl
import win32com.client
import os
import time
from datetime import datetime
import random

class GatoradeAllChannelsOptimizer:
    def __init__(self, excel_path):
        self.excel_path = os.path.abspath(excel_path)
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.channels = []
        self.total_budget = 1000000
        self.start_row = 38
        self.end_row = 57
        self.input_cols = list(range(3, 19))  # C到R列
        self.output_col = 21  # U列
        
    def initialize_excel(self):
        """初始化Excel应用程序并保持打开状态"""
        print("正在初始化Excel...")
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False
            
            print(f"正在打开文件: {self.excel_path}")
            self.workbook = self.excel.Workbooks.Open(self.excel_path)
            self.worksheet = self.workbook.Worksheets("calculation")
            
            # 读取媒体渠道名称
            self.channels = []
            for col in self.input_cols:
                cell_value = self.worksheet.Cells(1, col).Value
                if cell_value:
                    self.channels.append(str(cell_value).strip())
            
            print(f"成功读取到 {len(self.channels)} 个媒体渠道:")
            for i, channel in enumerate(self.channels, 1):
                print(f"  {i:2d}. {channel}")
                
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            self.cleanup()
            return False
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算数据并计算Y_predict总和"""
        try:
            # 写入预算数据到C38:R57
            for row in range(self.start_row, self.end_row + 1):
                for i, budget in enumerate(budgets):
                    col = self.input_cols[i]
                    self.worksheet.Cells(row, col).Value = budget
            
            # 强制重新计算
            self.worksheet.Calculate()
            
            # 读取U38:U57的Y_predict值并求和
            total_y = 0
            for row in range(self.start_row, self.end_row + 1):
                y_value = self.worksheet.Cells(row, self.output_col).Value
                if y_value is not None:
                    total_y += float(y_value)
            
            return total_y
        except Exception as e:
            print(f"计算Y_predict时出错: {e}")
            return 0
    
    def test_individual_channel_performance(self):
        """测试每个渠道的单独表现，找出高效渠道"""
        print("\n=== 第1步：测试各渠道单独表现 ===")
        
        channel_performance = []
        
        # 基准：平均分配
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        baseline_y = self.write_budgets_and_calculate(equal_budgets)
        print(f"基准方案（平均分配）: Y_predict = {baseline_y:,.2f}")
        
        # 测试每个渠道获得更多预算时的表现
        for i, channel in enumerate(self.channels):
            print(f"测试渠道 {i+1:2d}/16: {channel}")
            
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            y_predict = self.write_budgets_and_calculate(test_budgets)
            improvement = y_predict - baseline_y
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'y_predict': y_predict,
                'improvement': improvement,
                'efficiency': improvement / (self.total_budget * 0.3)  # 每单位预算的提升
            })
            
            print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f}")
        
        # 按效率排序
        channel_performance.sort(key=lambda x: x['efficiency'], reverse=True)
        
        print(f"\n渠道效率排名（每单位预算的Y_predict提升）:")
        print("-" * 60)
        for i, perf in enumerate(channel_performance, 1):
            print(f"{i:2d}. {perf['channel']:<15}: 效率 {perf['efficiency']:>8.2f}, 提升 {perf['improvement']:>+8,.0f}")
        
        return channel_performance, baseline_y
    
    def generate_smart_combinations(self, channel_performance, baseline_y):
        """基于渠道表现生成智能组合方案"""
        print(f"\n=== 第2步：生成智能组合方案 ===")
        
        results = []
        
        # 添加基准方案
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        results.append({
            "方案名称": "基准方案（平均分配）",
            "Y_predict": baseline_y,
            "预算分配": dict(zip(self.channels, equal_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 获取高效渠道
        top_channels = [perf['channel'] for perf in channel_performance[:8]]  # 前8个高效渠道
        medium_channels = [perf['channel'] for perf in channel_performance[8:12]]  # 中等渠道
        low_channels = [perf['channel'] for perf in channel_performance[12:]]  # 低效渠道
        
        print(f"高效渠道: {top_channels}")
        print(f"中等渠道: {medium_channels}")
        print(f"低效渠道: {low_channels}")
        
        # 方案1: 极致高效渠道集中
        print(f"\n测试方案1: 极致高效渠道集中")
        budgets = [0.0] * len(self.channels)
        high_budget = self.total_budget * 0.8 / len(top_channels[:4])  # 前4个高效渠道分80%
        low_budget = self.total_budget * 0.2 / (len(self.channels) - 4)  # 其他渠道分20%
        
        for i, channel in enumerate(self.channels):
            if channel in top_channels[:4]:
                budgets[i] = high_budget
            else:
                budgets[i] = low_budget
        
        y_predict = self.write_budgets_and_calculate(budgets)
        improvement = y_predict - baseline_y
        improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
        
        results.append({
            "方案名称": "极致高效渠道集中",
            "Y_predict": y_predict,
            "预算分配": dict(zip(self.channels, budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        # 方案2: 高效渠道优先
        print(f"\n测试方案2: 高效渠道优先")
        budgets = [0.0] * len(self.channels)
        high_budget = self.total_budget * 0.6 / len(top_channels)  # 高效渠道分60%
        medium_budget = self.total_budget * 0.25 / len(medium_channels)  # 中等渠道分25%
        low_budget = self.total_budget * 0.15 / len(low_channels)  # 低效渠道分15%
        
        for i, channel in enumerate(self.channels):
            if channel in top_channels:
                budgets[i] = high_budget
            elif channel in medium_channels:
                budgets[i] = medium_budget
            else:
                budgets[i] = low_budget
        
        y_predict = self.write_budgets_and_calculate(budgets)
        improvement = y_predict - baseline_y
        improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
        
        results.append({
            "方案名称": "高效渠道优先",
            "Y_predict": y_predict,
            "预算分配": dict(zip(self.channels, budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        # 方案3: 渐进式优化
        print(f"\n测试方案3: 渐进式优化")
        budgets = [self.total_budget / len(self.channels)] * len(self.channels)  # 从平均分配开始
        
        # 根据效率调整预算：高效渠道增加，低效渠道减少
        for i, channel in enumerate(self.channels):
            perf = next(p for p in channel_performance if p['channel'] == channel)
            rank = channel_performance.index(perf) + 1
            
            if rank <= 4:  # 前4名，增加50%
                budgets[i] *= 1.5
            elif rank <= 8:  # 5-8名，增加20%
                budgets[i] *= 1.2
            elif rank <= 12:  # 9-12名，保持不变
                budgets[i] *= 1.0
            else:  # 13-16名，减少30%
                budgets[i] *= 0.7
        
        # 重新归一化到总预算
        total_budget_check = sum(budgets)
        budgets = [b * self.total_budget / total_budget_check for b in budgets]
        
        y_predict = self.write_budgets_and_calculate(budgets)
        improvement = y_predict - baseline_y
        improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
        
        results.append({
            "方案名称": "渐进式优化",
            "Y_predict": y_predict,
            "预算分配": dict(zip(self.channels, budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        return results
    
    def run_random_search_optimization(self, channel_performance, baseline_y, num_iterations=100):
        """运行随机搜索优化，覆盖所有渠道"""
        print(f"\n=== 第3步：随机搜索优化（{num_iterations}次迭代）===")
        
        best_results = []
        
        # 获取渠道效率权重
        channel_weights = {}
        for perf in channel_performance:
            # 将效率转换为正权重（最低效率+1作为基准）
            min_efficiency = min(p['efficiency'] for p in channel_performance)
            normalized_efficiency = perf['efficiency'] - min_efficiency + 1
            channel_weights[perf['channel']] = max(normalized_efficiency, 0.1)
        
        print("开始随机搜索...")
        for iteration in range(num_iterations):
            if iteration % 20 == 0:
                print(f"  进度: {iteration}/{num_iterations}")
            
            # 生成随机权重，但偏向高效渠道
            weights = []
            for channel in self.channels:
                base_weight = channel_weights[channel]
                # 添加随机性，但保持偏向性
                random_factor = random.uniform(0.5, 2.0)
                weights.append(base_weight * random_factor)
            
            # 归一化权重
            total_weight = sum(weights)
            ratios = [w / total_weight for w in weights]
            budgets = [r * self.total_budget for r in ratios]
            
            # 计算Y_predict
            y_predict = self.write_budgets_and_calculate(budgets)
            improvement = y_predict - baseline_y
            improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
            
            # 保存好的结果
            if improvement > 0:
                best_results.append({
                    "方案名称": f"随机搜索方案{len(best_results)+1}",
                    "Y_predict": y_predict,
                    "预算分配": dict(zip(self.channels, budgets)),
                    "提升": improvement,
                    "提升比例": f"{improvement_pct:.2f}%"
                })
        
        # 按Y_predict排序，取前5个
        best_results.sort(key=lambda x: x["Y_predict"], reverse=True)
        best_results = best_results[:5]
        
        print(f"随机搜索完成，找到 {len(best_results)} 个优秀方案")
        for i, result in enumerate(best_results, 1):
            print(f"  方案{i}: Y_predict = {result['Y_predict']:,.2f}, 提升 = {result['提升']:+,.2f}")
        
        return best_results
    
    def run_comprehensive_optimization(self):
        """运行全面的优化分析"""
        print("=" * 80)
        print("Gatorade 媒体投放全渠道优化分析")
        print("=" * 80)

        # 第1步：测试各渠道单独表现
        channel_performance, baseline_y = self.test_individual_channel_performance()

        # 第2步：生成智能组合方案
        smart_results = self.generate_smart_combinations(channel_performance, baseline_y)

        # 第3步：随机搜索优化
        random_results = self.run_random_search_optimization(channel_performance, baseline_y, num_iterations=200)

        # 合并所有结果
        all_results = smart_results + random_results

        # 按Y_predict排序
        all_results.sort(key=lambda x: x["Y_predict"], reverse=True)

        return all_results, baseline_y, channel_performance

    def export_results_to_excel(self, results, baseline_y, channel_performance):
        """将结果导出到Excel文件"""
        try:
            # 创建渠道分析工作表
            try:
                analysis_sheet = self.workbook.Worksheets("渠道效率分析")
                analysis_sheet.Delete()
            except:
                pass

            analysis_sheet = self.workbook.Worksheets.Add()
            analysis_sheet.Name = "渠道效率分析"

            # 写入渠道分析表头
            headers = ["排名", "渠道名称", "单独测试Y_predict", "vs基准提升", "效率(提升/预算)"]
            for i, header in enumerate(headers, 1):
                analysis_sheet.Cells(1, i).Value = header
                analysis_sheet.Cells(1, i).Font.Bold = True

            # 写入渠道分析数据
            for rank, perf in enumerate(channel_performance, 1):
                row = rank + 1
                analysis_sheet.Cells(row, 1).Value = rank
                analysis_sheet.Cells(row, 2).Value = perf['channel']
                analysis_sheet.Cells(row, 3).Value = perf['y_predict']
                analysis_sheet.Cells(row, 4).Value = perf['improvement']
                analysis_sheet.Cells(row, 5).Value = perf['efficiency']

            analysis_sheet.Columns.AutoFit()

            # 创建方案对比工作表
            try:
                comparison_sheet = self.workbook.Worksheets("全渠道优化结果")
                comparison_sheet.Delete()
            except:
                pass

            comparison_sheet = self.workbook.Worksheets.Add()
            comparison_sheet.Name = "全渠道优化结果"

            # 写入方案对比表头
            headers = ["排名", "方案名称", "Y_predict", "提升", "提升比例"] + self.channels
            for i, header in enumerate(headers, 1):
                comparison_sheet.Cells(1, i).Value = header
                comparison_sheet.Cells(1, i).Font.Bold = True

            # 写入方案对比数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                comparison_sheet.Cells(row, 1).Value = rank
                comparison_sheet.Cells(row, 2).Value = result["方案名称"]
                comparison_sheet.Cells(row, 3).Value = result["Y_predict"]
                comparison_sheet.Cells(row, 4).Value = result["提升"]
                comparison_sheet.Cells(row, 5).Value = result["提升比例"]

                # 写入各渠道预算配比
                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / self.total_budget
                    comparison_sheet.Cells(row, 6 + i).Value = ratio
                    comparison_sheet.Cells(row, 6 + i).NumberFormat = "0.00%"

            comparison_sheet.Columns.AutoFit()

            # 保存文件
            self.workbook.Save()
            print(f"\n结果已导出到Excel文件:")
            print(f"  - '渠道效率分析'工作表: 各渠道单独表现分析")
            print(f"  - '全渠道优化结果'工作表: 所有优化方案对比")

        except Exception as e:
            print(f"导出Excel时出错: {e}")

    def print_final_report(self, results, baseline_y, channel_performance):
        """打印最终分析报告"""
        print("\n" + "=" * 80)
        print("最终分析报告")
        print("=" * 80)

        print(f"\n基准方案（平均分配）Y_predict: {baseline_y:,.2f}")
        print(f"总预算: {self.total_budget:,}")

        # 渠道效率总结
        print(f"\n渠道效率总结（前8名）:")
        print("-" * 50)
        for i, perf in enumerate(channel_performance[:8], 1):
            print(f"{i}. {perf['channel']:<15}: 效率 {perf['efficiency']:>6.2f}")

        # 最优方案对比
        print(f"\n最优方案对比（前5名）:")
        print("-" * 80)
        print(f"{'排名':<4} {'方案名称':<25} {'Y_predict':<12} {'提升':<10} {'提升比例':<8}")
        print("-" * 80)

        for i, result in enumerate(results[:5], 1):
            print(f"{i:<4} {result['方案名称']:<25} {result['Y_predict']:>10,.0f} {result['提升']:>8,.0f} {result['提升比例']:>7}")

        # 最优方案详细分析
        best_result = results[0]
        print(f"\n最优方案详细分析: {best_result['方案名称']}")
        print("-" * 60)

        # 按预算从高到低排序
        budget_items = [(channel, budget) for channel, budget in best_result["预算分配"].items()]
        budget_items.sort(key=lambda x: x[1], reverse=True)

        print("渠道预算分配:")
        for channel, budget in budget_items:
            ratio = budget / self.total_budget
            print(f"  {channel:<15}: {ratio:>6.1%} ({budget:>10,.0f})")

        # 渠道类型汇总
        print(f"\n渠道类型汇总:")
        digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
        kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
        outdoor_channels = ['BuildingLCD', 'Metro', 'CreativeOutdoor']
        sponsor_channels = ['SponsorEvent', 'SponsorDrama']
        push_channels = ['WeiboPush', 'DouyinPush']

        digital_budget = sum(best_result["预算分配"][ch] for ch in digital_channels if ch in self.channels)
        kol_budget = sum(best_result["预算分配"][ch] for ch in kol_channels if ch in self.channels)
        outdoor_budget = sum(best_result["预算分配"][ch] for ch in outdoor_channels if ch in self.channels)
        sponsor_budget = sum(best_result["预算分配"][ch] for ch in sponsor_channels if ch in self.channels)
        push_budget = sum(best_result["预算分配"][ch] for ch in push_channels if ch in self.channels)

        print(f"  数字媒体: {digital_budget/self.total_budget:>6.1%} ({digital_budget:>10,.0f})")
        print(f"  KOL营销:  {kol_budget/self.total_budget:>6.1%} ({kol_budget:>10,.0f})")
        print(f"  户外媒体: {outdoor_budget/self.total_budget:>6.1%} ({outdoor_budget:>10,.0f})")
        print(f"  赞助活动: {sponsor_budget/self.total_budget:>6.1%} ({sponsor_budget:>10,.0f})")
        print(f"  信息流推广: {push_budget/self.total_budget:>6.1%} ({push_budget:>10,.0f})")

        # 输出原始数据供验证
        print(f"\n" + "=" * 80)
        print("原始输入数据（供Excel验证）")
        print("=" * 80)

        for i, result in enumerate(results[:3], 1):
            print(f"\n方案{i}: {result['方案名称']}")
            print("C38:R57区域输入值（每行相同）:")
            budgets = [result["预算分配"][ch] for ch in self.channels]
            budget_str = ", ".join([f"{budget:.2f}" for budget in budgets])
            print(f"[{budget_str}]")

    def cleanup(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass


def main():
    """主函数"""
    excel_file = "Gatorade simulation tool_cal.xlsx"

    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return

    optimizer = GatoradeAllChannelsOptimizer(excel_file)

    try:
        # 初始化Excel
        if not optimizer.initialize_excel():
            print("Excel初始化失败，程序退出")
            return

        # 运行全面优化分析
        start_time = datetime.now()
        print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        results, baseline_y, channel_performance = optimizer.run_comprehensive_optimization()

        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")

        # 导出结果到Excel
        optimizer.export_results_to_excel(results, baseline_y, channel_performance)

        # 打印最终报告
        optimizer.print_final_report(results, baseline_y, channel_performance)

        print(f"\n优化完成！请查看Excel文件中的分析结果。")

    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        optimizer.cleanup()
        print("\n资源清理完成")


if __name__ == "__main__":
    main()
