#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数自动更新功能
1. 界面参数修改后自动更新配置
2. 无需手动保存即可生效
3. 验证迭代次数和方案数量的实时更新
"""

import tkinter as tk
from universal_media_optimizer_v2 import UniversalMediaOptimizerV2
import time

def test_parameter_auto_update():
    """测试参数自动更新功能"""
    print("🧪 测试参数自动更新功能")
    print("=" * 50)
    
    # 创建主窗口
    root = tk.Tk()
    app = UniversalMediaOptimizerV2(root)
    
    # 模拟界面操作
    print("📊 初始配置:")
    print(f"   迭代次数: {app.config.get('max_iterations', 'N/A')}")
    print(f"   方案数量: {app.config.get('optimization_schemes', 'N/A')}")
    
    # 测试迭代次数自动更新
    print(f"\n🔄 测试迭代次数自动更新:")
    original_iterations = app.iterations_var.get()
    new_iterations = 500
    
    print(f"   修改前: {original_iterations}")
    app.iterations_var.set(new_iterations)
    
    # 给一点时间让回调函数执行
    root.update()
    time.sleep(0.1)
    
    print(f"   修改后: {app.config.get('max_iterations', 'N/A')}")
    print(f"   界面值: {app.iterations_var.get()}")
    
    # 验证是否自动更新
    if app.config.get('max_iterations') == new_iterations:
        print(f"   ✅ 迭代次数自动更新成功")
    else:
        print(f"   ❌ 迭代次数自动更新失败")
    
    # 测试方案数量自动更新
    print(f"\n🔄 测试方案数量自动更新:")
    original_schemes = app.schemes_var.get()
    new_schemes = 8
    
    print(f"   修改前: {original_schemes}")
    app.schemes_var.set(new_schemes)
    
    # 给一点时间让回调函数执行
    root.update()
    time.sleep(0.1)
    
    print(f"   修改后: {app.config.get('optimization_schemes', 'N/A')}")
    print(f"   界面值: {app.schemes_var.get()}")
    
    # 验证是否自动更新
    if app.config.get('optimization_schemes') == new_schemes:
        print(f"   ✅ 方案数量自动更新成功")
    else:
        print(f"   ❌ 方案数量自动更新失败")
    
    # 测试多次修改
    print(f"\n🔄 测试多次快速修改:")
    test_values = [300, 400, 600, 800]
    
    for i, value in enumerate(test_values, 1):
        app.iterations_var.set(value)
        root.update()
        time.sleep(0.05)  # 短暂延迟
        
        current_config_value = app.config.get('max_iterations')
        print(f"   第{i}次修改: 设置{value} → 配置{current_config_value} {'✅' if current_config_value == value else '❌'}")
    
    # 测试边界值
    print(f"\n🔄 测试边界值:")
    boundary_tests = [
        ("最小值", 1),
        ("较小值", 10),
        ("中等值", 200),
        ("较大值", 1000),
        ("很大值", 5000)
    ]
    
    for test_name, value in boundary_tests:
        app.iterations_var.set(value)
        root.update()
        time.sleep(0.05)
        
        current_config_value = app.config.get('max_iterations')
        status = "✅" if current_config_value == value else "❌"
        print(f"   {test_name} ({value}): {status}")
    
    # 关闭窗口
    root.destroy()
    
    print(f"\n✅ 参数自动更新测试完成")
    return True

def test_config_persistence():
    """测试配置持久化"""
    print(f"\n🧪 测试配置持久化")
    print("=" * 50)
    
    # 第一次创建应用
    print("📊 第一次创建应用:")
    root1 = tk.Tk()
    app1 = UniversalMediaOptimizerV2(root1)
    
    # 修改参数
    test_iterations = 777
    test_schemes = 9
    
    app1.iterations_var.set(test_iterations)
    app1.schemes_var.set(test_schemes)
    root1.update()
    time.sleep(0.1)
    
    print(f"   设置迭代次数: {test_iterations}")
    print(f"   设置方案数量: {test_schemes}")
    print(f"   配置中迭代次数: {app1.config.get('max_iterations')}")
    print(f"   配置中方案数量: {app1.config.get('optimization_schemes')}")
    
    # 模拟保存配置到文件
    config_data = app1.config.copy()
    
    root1.destroy()
    
    # 第二次创建应用，模拟重新启动
    print(f"\n📊 第二次创建应用（模拟重启）:")
    root2 = tk.Tk()
    app2 = UniversalMediaOptimizerV2(root2)
    
    # 手动加载之前的配置
    app2.config.update(config_data)
    app2.update_ui_from_config()
    
    print(f"   加载后迭代次数: {app2.iterations_var.get()}")
    print(f"   加载后方案数量: {app2.schemes_var.get()}")
    print(f"   配置中迭代次数: {app2.config.get('max_iterations')}")
    print(f"   配置中方案数量: {app2.config.get('optimization_schemes')}")
    
    # 验证配置是否正确恢复
    iterations_match = app2.iterations_var.get() == test_iterations
    schemes_match = app2.schemes_var.get() == test_schemes
    
    print(f"\n验证结果:")
    print(f"   迭代次数恢复: {'✅' if iterations_match else '❌'}")
    print(f"   方案数量恢复: {'✅' if schemes_match else '❌'}")
    
    root2.destroy()
    
    print(f"\n✅ 配置持久化测试完成")
    return iterations_match and schemes_match

def test_update_config_from_ui():
    """测试从界面更新配置的功能"""
    print(f"\n🧪 测试从界面更新配置功能")
    print("=" * 50)
    
    root = tk.Tk()
    app = UniversalMediaOptimizerV2(root)
    
    # 修改多个界面参数
    test_params = {
        "iterations": 999,
        "schemes": 7,
        "budget": 2000000,
        "worksheet": "test_sheet"
    }
    
    print("📊 修改界面参数:")
    app.iterations_var.set(test_params["iterations"])
    app.schemes_var.set(test_params["schemes"])
    app.budget_var.set(test_params["budget"])
    app.worksheet_var.set(test_params["worksheet"])
    
    print(f"   迭代次数: {test_params['iterations']}")
    print(f"   方案数量: {test_params['schemes']}")
    print(f"   总预算: {test_params['budget']}")
    print(f"   工作表: {test_params['worksheet']}")
    
    # 调用更新配置方法
    print(f"\n🔄 调用 update_config_from_ui():")
    app.update_config_from_ui()
    
    # 验证配置是否正确更新
    print(f"   配置中迭代次数: {app.config.get('max_iterations')}")
    print(f"   配置中方案数量: {app.config.get('optimization_schemes')}")
    print(f"   配置中总预算: {app.config.get('total_budget')}")
    print(f"   配置中工作表: {app.config.get('worksheet_name')}")
    
    # 验证结果
    results = {
        "iterations": app.config.get('max_iterations') == test_params["iterations"],
        "schemes": app.config.get('optimization_schemes') == test_params["schemes"],
        "budget": app.config.get('total_budget') == test_params["budget"],
        "worksheet": app.config.get('worksheet_name') == test_params["worksheet"]
    }
    
    print(f"\n验证结果:")
    for param, is_correct in results.items():
        print(f"   {param}: {'✅' if is_correct else '❌'}")
    
    root.destroy()
    
    all_correct = all(results.values())
    print(f"\n✅ 从界面更新配置测试{'成功' if all_correct else '失败'}")
    return all_correct

def test_real_optimization_with_updated_params():
    """测试使用更新参数的真实优化"""
    print(f"\n🧪 测试使用更新参数的真实优化")
    print("=" * 50)
    
    # 模拟优化配置
    from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
    
    # 创建测试配置
    config = {
        "excel_file": "dummy.xlsx",
        "worksheet_name": "calculation",
        "input_start_row": 38, "input_end_row": 57,
        "input_start_col": 3, "input_end_col": 18,
        "output_col": 21, "output_start_row": 38, "output_end_row": 57,
        "channel_names_row": 1, "total_budget": 1000000,
        "optimization_schemes": 3, "max_iterations": 50,  # 初始值
        "baseline_allocation": "equal", "custom_baseline_ratios": {}, "channel_constraints": {}
    }
    
    channels = ["SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll"]
    
    print(f"📊 初始配置:")
    print(f"   迭代次数: {config['max_iterations']}")
    print(f"   方案数量: {config['optimization_schemes']}")
    
    # 模拟界面修改参数
    print(f"\n🔄 模拟界面修改参数:")
    config["max_iterations"] = 200  # 模拟界面修改
    config["optimization_schemes"] = 5
    
    print(f"   新迭代次数: {config['max_iterations']}")
    print(f"   新方案数量: {config['optimization_schemes']}")
    
    # 创建优化器并验证参数
    print(f"\n🚀 创建优化器:")
    
    def progress_callback(progress, status, detail=""):
        if progress % 50 == 0:
            print(f"   进度: {progress:.0f}% - {status}")
    
    try:
        optimizer = UniversalOptimizerEngineV2(config, channels, progress_callback)
        
        # 验证优化器是否使用了正确的参数
        print(f"   优化器配置中迭代次数: {optimizer.config.get('max_iterations')}")
        print(f"   优化器配置中方案数量: {optimizer.config.get('optimization_schemes')}")
        
        # 验证参数是否正确传递
        iterations_correct = optimizer.config.get('max_iterations') == 200
        schemes_correct = optimizer.config.get('optimization_schemes') == 5
        
        print(f"\n验证结果:")
        print(f"   迭代次数传递: {'✅' if iterations_correct else '❌'}")
        print(f"   方案数量传递: {'✅' if schemes_correct else '❌'}")
        
        print(f"\n✅ 参数传递测试{'成功' if iterations_correct and schemes_correct else '失败'}")
        return iterations_correct and schemes_correct
        
    except Exception as e:
        print(f"❌ 优化器创建失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试参数自动更新系统")
    print("=" * 60)
    
    tests = [
        ("参数自动更新", test_parameter_auto_update),
        ("配置持久化", test_config_persistence),
        ("界面更新配置", test_update_config_from_ui),
        ("真实优化参数", test_real_optimization_with_updated_params)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print(f"🎉 参数自动更新系统验证成功！")
        print(f"\n✨ 修复内容:")
        print(f"   ✅ 修复了save_config中缺少max_iterations的问题")
        print(f"   ✅ 添加了迭代次数的实时自动更新")
        print(f"   ✅ 添加了方案数量的实时自动更新")
        print(f"   ✅ 确保界面修改后立即生效")
        print(f"   ✅ 无需手动保存配置")
        print(f"\n💡 使用说明:")
        print(f"   - 修改界面中的迭代次数后会立即生效")
        print(f"   - 修改界面中的方案数量后会立即生效")
        print(f"   - 开始优化时会自动使用最新的界面参数")
        print(f"   - 不再需要手动保存配置文件")
    else:
        print(f"⚠️  部分测试失败，请检查相关功能")
    
    print(f"\n按回车键退出...")
    input()
