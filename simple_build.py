#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的EXE打包脚本
快速生成可执行文件
"""

import os
import sys
import subprocess

def simple_build():
    """简化的打包流程"""
    print("🚀 媒体预算优化工具 - 简化打包")
    print("=" * 50)
    
    # 检查主文件
    if not os.path.exists('universal_media_optimizer_v2.py'):
        print("❌ 未找到主程序文件")
        return False
    
    print("📦 安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ PyInstaller安装完成")
    except:
        print("⚠️  PyInstaller可能已安装")
    
    print("🔨 开始打包...")
    
    # 简化的打包命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed", 
        "--name=媒体预算优化工具",
        "--clean",
        "universal_media_optimizer_v2.py"
    ]
    
    try:
        print("执行打包命令...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 打包成功")
            
            # 检查输出文件
            exe_path = "dist/媒体预算优化工具.exe"
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path) / (1024*1024)
                print(f"📁 EXE文件: {exe_path}")
                print(f"📊 文件大小: {size:.1f} MB")
                
                # 复制Excel文件
                excel_file = "Gatorade simulation tool_cal.xlsx"
                if os.path.exists(excel_file):
                    import shutil
                    shutil.copy2(excel_file, "dist/")
                    print(f"✅ 复制示例文件: {excel_file}")
                
                return True
            else:
                print("❌ 未找到生成的EXE文件")
                return False
        else:
            print("❌ 打包失败")
            print("错误信息:", result.stderr[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 打包超时，请稍后重试")
        return False
    except Exception as e:
        print(f"❌ 打包异常: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        if simple_build():
            print("\n🎉 打包完成！")
            print("📁 文件位置: dist/媒体预算优化工具.exe")
            print("💡 可以将dist目录中的文件分发给其他用户")
        else:
            print("\n❌ 打包失败")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    
    input("\n按回车键退出...")
