import pandas as pd
import numpy as np
import openpyxl
import win32com.client
import os
import time
from datetime import datetime
import random

class GatoradeOptimizerCorrected:
    def __init__(self, excel_path):
        self.excel_path = os.path.abspath(excel_path)
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.channels = []
        self.total_budget = 1000000
        self.start_row = 38
        self.end_row = 57
        self.input_cols = list(range(3, 19))  # C到R列
        self.output_col = 21  # U列
        
    def initialize_excel(self):
        """初始化Excel应用程序并保持打开状态"""
        print("正在初始化Excel...")
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False
            
            print(f"正在打开文件: {self.excel_path}")
            self.workbook = self.excel.Workbooks.Open(self.excel_path)
            self.worksheet = self.workbook.Worksheets("calculation")
            
            # 读取媒体渠道名称
            self.channels = []
            for col in self.input_cols:
                cell_value = self.worksheet.Cells(1, col).Value
                if cell_value:
                    self.channels.append(str(cell_value).strip())
            
            print(f"成功读取到 {len(self.channels)} 个媒体渠道:")
            for i, channel in enumerate(self.channels, 1):
                print(f"  {i:2d}. {channel}")
                
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            self.cleanup()
            return False
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算数据并计算Y_predict总和"""
        try:
            # 清空输入区域
            input_range = self.worksheet.Range("C38:R57")
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.end_row - self.start_row + 1  # 20行
            budgets_per_row = [budget / num_rows for budget in budgets]
            
            # 写入预算数据到C38:R57
            for row in range(self.start_row, self.end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_cols[i]
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.5)  # 等待计算完成
            
            # 读取U38:U57的Y_predict值并求和
            total_y = 0
            for row in range(self.start_row, self.end_row + 1):
                y_value = self.worksheet.Cells(row, self.output_col).Value
                if y_value is not None:
                    total_y += float(y_value)
            
            return total_y
        except Exception as e:
            print(f"计算Y_predict时出错: {e}")
            return 0
    
    def verify_baseline(self):
        """验证基准方案计算"""
        print("\n=== 验证基准方案 ===")
        
        # 平均分配：每个渠道62,500，分配到20行就是每行每个渠道3,125
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        baseline_y = self.write_budgets_and_calculate(equal_budgets)
        
        print(f"平均分配预算: 每个渠道 {equal_budgets[0]:,.0f}")
        print(f"每行每个渠道: {equal_budgets[0]/20:,.0f}")
        print(f"基准方案Y_predict: {baseline_y:,.2f}")
        
        # 验证总预算
        total_check = sum(equal_budgets)
        print(f"总预算验证: {total_check:,.0f}")
        
        return baseline_y, equal_budgets
    
    def test_individual_channels(self, baseline_y):
        """测试各渠道单独表现"""
        print(f"\n=== 测试各渠道单独表现 ===")
        
        channel_performance = []
        
        for i, channel in enumerate(self.channels):
            print(f"测试渠道 {i+1:2d}/16: {channel}")
            
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            y_predict = self.write_budgets_and_calculate(test_budgets)
            improvement = y_predict - baseline_y
            efficiency = improvement / (self.total_budget * 0.3)  # 每单位预算的提升
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'y_predict': y_predict,
                'improvement': improvement,
                'efficiency': efficiency
            })
            
            print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f}")
        
        # 按效率排序
        channel_performance.sort(key=lambda x: x['improvement'], reverse=True)
        
        print(f"\n渠道效率排名:")
        print("-" * 60)
        for i, perf in enumerate(channel_performance, 1):
            print(f"{i:2d}. {perf['channel']:<15}: 提升 {perf['improvement']:>+8,.0f}")
        
        return channel_performance
    
    def generate_optimized_schemes(self, baseline_y, channel_performance):
        """生成优化方案"""
        print(f"\n=== 生成优化方案 ===")
        
        results = []
        
        # 添加基准方案
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        results.append({
            "方案名称": "基准方案（平均分配）",
            "Y_predict": baseline_y,
            "预算分配": dict(zip(self.channels, equal_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 获取高效渠道
        top_channels = [perf['channel'] for perf in channel_performance[:6] if perf['improvement'] > 0]
        print(f"高效渠道: {top_channels}")
        
        # 方案1: 高效渠道集中（前4个高效渠道各分配20%，其他渠道平分剩余20%）
        if len(top_channels) >= 4:
            print(f"\n测试方案1: 高效渠道集中")
            budgets = [0.0] * len(self.channels)
            
            # 前4个高效渠道各20%
            for i, channel in enumerate(self.channels):
                if channel in top_channels[:4]:
                    budgets[i] = self.total_budget * 0.2
                else:
                    budgets[i] = self.total_budget * 0.2 / (len(self.channels) - 4)
            
            y_predict = self.write_budgets_and_calculate(budgets)
            improvement = y_predict - baseline_y
            improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
            
            results.append({
                "方案名称": "高效渠道集中",
                "Y_predict": y_predict,
                "预算分配": dict(zip(self.channels, budgets)),
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
            print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        # 方案2: 渐进式优化（根据表现调整权重）
        print(f"\n测试方案2: 渐进式优化")
        budgets = [self.total_budget / len(self.channels)] * len(self.channels)  # 从平均分配开始
        
        # 根据表现调整预算
        for i, channel in enumerate(self.channels):
            perf = next(p for p in channel_performance if p['channel'] == channel)
            rank = channel_performance.index(perf) + 1
            
            if rank <= 3:  # 前3名，增加60%
                budgets[i] *= 1.6
            elif rank <= 6:  # 4-6名，增加30%
                budgets[i] *= 1.3
            elif rank <= 10:  # 7-10名，保持不变
                budgets[i] *= 1.0
            else:  # 11-16名，减少40%
                budgets[i] *= 0.6
        
        # 重新归一化到总预算
        total_budget_check = sum(budgets)
        budgets = [b * self.total_budget / total_budget_check for b in budgets]
        
        y_predict = self.write_budgets_and_calculate(budgets)
        improvement = y_predict - baseline_y
        improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
        
        results.append({
            "方案名称": "渐进式优化",
            "Y_predict": y_predict,
            "预算分配": dict(zip(self.channels, budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        # 方案3: 极致优化（最好的渠道获得更多预算）
        if len(top_channels) >= 2:
            print(f"\n测试方案3: 极致优化")
            budgets = [0.0] * len(self.channels)
            
            # 最好的2个渠道各30%，其他渠道平分剩余40%
            for i, channel in enumerate(self.channels):
                if channel in top_channels[:2]:
                    budgets[i] = self.total_budget * 0.3
                else:
                    budgets[i] = self.total_budget * 0.4 / (len(self.channels) - 2)
            
            y_predict = self.write_budgets_and_calculate(budgets)
            improvement = y_predict - baseline_y
            improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
            
            results.append({
                "方案名称": "极致优化",
                "Y_predict": y_predict,
                "预算分配": dict(zip(self.channels, budgets)),
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
            print(f"  Y_predict: {y_predict:,.2f}, 提升: {improvement:+,.2f} ({improvement_pct:+.2f}%)")
        
        return results
    
    def run_optimization(self):
        """运行完整的优化分析"""
        print("=" * 80)
        print("Gatorade 媒体投放优化分析（修正版）")
        print("=" * 80)
        
        # 1. 验证基准方案
        baseline_y, baseline_budgets = self.verify_baseline()
        
        # 2. 测试各渠道表现
        channel_performance = self.test_individual_channels(baseline_y)
        
        # 3. 生成优化方案
        results = self.generate_optimized_schemes(baseline_y, channel_performance)
        
        # 按Y_predict排序
        results.sort(key=lambda x: x["Y_predict"], reverse=True)
        
        return results, baseline_y, channel_performance
    
    def export_results_to_excel(self, results, baseline_y, channel_performance):
        """将结果导出到Excel文件"""
        try:
            # 创建方案对比工作表
            try:
                comparison_sheet = self.workbook.Worksheets("优化结果对比（修正版）")
                comparison_sheet.Delete()
            except:
                pass

            comparison_sheet = self.workbook.Worksheets.Add()
            comparison_sheet.Name = "优化结果对比（修正版）"

            # 写入方案对比表头
            headers = ["排名", "方案名称", "Y_predict", "提升", "提升比例"] + self.channels
            for i, header in enumerate(headers, 1):
                comparison_sheet.Cells(1, i).Value = header
                comparison_sheet.Cells(1, i).Font.Bold = True

            # 写入方案对比数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                comparison_sheet.Cells(row, 1).Value = rank
                comparison_sheet.Cells(row, 2).Value = result["方案名称"]
                comparison_sheet.Cells(row, 3).Value = result["Y_predict"]
                comparison_sheet.Cells(row, 4).Value = result["提升"]
                comparison_sheet.Cells(row, 5).Value = result["提升比例"]

                # 写入各渠道预算配比
                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / self.total_budget
                    comparison_sheet.Cells(row, 6 + i).Value = ratio
                    comparison_sheet.Cells(row, 6 + i).NumberFormat = "0.00%"

            comparison_sheet.Columns.AutoFit()

            # 保存文件
            self.workbook.Save()
            print(f"\n结果已导出到Excel文件的'优化结果对比（修正版）'工作表")

        except Exception as e:
            print(f"导出Excel时出错: {e}")

    def print_final_report(self, results, baseline_y, channel_performance):
        """打印最终分析报告"""
        print("\n" + "=" * 80)
        print("最终分析报告（修正版）")
        print("=" * 80)

        print(f"\n基准方案（平均分配）Y_predict: {baseline_y:,.2f}")
        print(f"总预算: {self.total_budget:,}")

        # 渠道效率总结
        print(f"\n渠道效率总结（按提升排序）:")
        print("-" * 60)
        for i, perf in enumerate(channel_performance[:10], 1):
            print(f"{i:2d}. {perf['channel']:<15}: 提升 {perf['improvement']:>+8,.0f}")

        # 最优方案对比
        print(f"\n最优方案对比:")
        print("-" * 80)
        print(f"{'排名':<4} {'方案名称':<25} {'Y_predict':<12} {'提升':<10} {'提升比例':<8}")
        print("-" * 80)

        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['方案名称']:<25} {result['Y_predict']:>10,.0f} {result['提升']:>8,.0f} {result['提升比例']:>7}")

        # 最优方案详细分析
        if len(results) > 1:
            best_result = results[0] if results[0]["方案名称"] != "基准方案（平均分配）" else results[1]
            print(f"\n最优方案详细分析: {best_result['方案名称']}")
            print("-" * 60)

            # 按预算从高到低排序
            budget_items = [(channel, budget) for channel, budget in best_result["预算分配"].items()]
            budget_items.sort(key=lambda x: x[1], reverse=True)

            print("渠道预算分配:")
            for channel, budget in budget_items:
                ratio = budget / self.total_budget
                print(f"  {channel:<15}: {ratio:>6.1%} ({budget:>10,.0f})")

        # 输出原始数据供验证
        print(f"\n" + "=" * 80)
        print("原始输入数据（供Excel验证）")
        print("=" * 80)

        for i, result in enumerate(results[:3], 1):
            print(f"\n方案{i}: {result['方案名称']}")
            print("每行输入值（C38:R57，每行相同）:")
            budgets = [result["预算分配"][ch] for ch in self.channels]
            budgets_per_row = [budget / 20 for budget in budgets]  # 分配到20行
            budget_str = ", ".join([f"{budget:.2f}" for budget in budgets_per_row])
            print(f"[{budget_str}]")

            print("各渠道总预算:")
            for channel, budget in zip(self.channels, budgets):
                print(f"  {channel}: {budget:,.0f}")

    def cleanup(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass


def main():
    """主函数"""
    excel_file = "Gatorade simulation tool_cal.xlsx"

    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return

    optimizer = GatoradeOptimizerCorrected(excel_file)

    try:
        # 初始化Excel
        if not optimizer.initialize_excel():
            print("Excel初始化失败，程序退出")
            return

        # 运行优化分析
        start_time = datetime.now()
        print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        results, baseline_y, channel_performance = optimizer.run_optimization()

        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")

        # 导出结果到Excel
        optimizer.export_results_to_excel(results, baseline_y, channel_performance)

        # 打印最终报告
        optimizer.print_final_report(results, baseline_y, channel_performance)

        print(f"\n优化完成！请查看Excel文件中的分析结果。")

    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        optimizer.cleanup()
        print("\n资源清理完成")


if __name__ == "__main__":
    main()
