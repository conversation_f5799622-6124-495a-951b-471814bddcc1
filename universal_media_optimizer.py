import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import win32com.client
import time
from datetime import datetime
import threading

class UniversalMediaOptimizer:
    def __init__(self, root):
        self.root = root
        self.root.title("通用媒体预算优化工具 v1.0")
        self.root.geometry("800x700")
        
        # 配置变量
        self.config = {
            "excel_file": "",
            "worksheet_name": "calculation",
            "input_start_row": 38,
            "input_end_row": 57,
            "input_start_col": 3,  # C列
            "input_end_col": 18,   # R列
            "output_col": 21,      # U列
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 5,
            "baseline_allocation": "equal",  # equal, custom
            "channel_constraints": {}
        }
        
        self.channels = []
        self.excel = None
        self.workbook = None
        self.worksheet = None
        
        self.create_widgets()
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 文件设置", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar(value=self.config["excel_file"])
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        
        ttk.Label(file_frame, text="工作表名称:").grid(row=1, column=0, sticky=tk.W)
        self.worksheet_var = tk.StringVar(value=self.config["worksheet_name"])
        ttk.Entry(file_frame, textvariable=self.worksheet_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 区域设置
        area_frame = ttk.LabelFrame(main_frame, text="2. 数据区域设置", padding="10")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 输入区域
        ttk.Label(area_frame, text="输入区域:").grid(row=0, column=0, sticky=tk.W)
        input_frame = ttk.Frame(area_frame)
        input_frame.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(input_frame, text="行:").grid(row=0, column=0)
        self.start_row_var = tk.IntVar(value=self.config["input_start_row"])
        ttk.Entry(input_frame, textvariable=self.start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(input_frame, text="到").grid(row=0, column=2)
        self.end_row_var = tk.IntVar(value=self.config["input_end_row"])
        ttk.Entry(input_frame, textvariable=self.end_row_var, width=5).grid(row=0, column=3)
        
        ttk.Label(input_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.start_col_var = tk.IntVar(value=self.config["input_start_col"])
        ttk.Entry(input_frame, textvariable=self.start_col_var, width=5).grid(row=0, column=5)
        ttk.Label(input_frame, text="到").grid(row=0, column=6)
        self.end_col_var = tk.IntVar(value=self.config["input_end_col"])
        ttk.Entry(input_frame, textvariable=self.end_col_var, width=5).grid(row=0, column=7)
        
        # 输出列
        ttk.Label(area_frame, text="输出列:").grid(row=1, column=0, sticky=tk.W)
        self.output_col_var = tk.IntVar(value=self.config["output_col"])
        ttk.Entry(area_frame, textvariable=self.output_col_var, width=5).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 渠道名称行
        ttk.Label(area_frame, text="渠道名称行:").grid(row=2, column=0, sticky=tk.W)
        self.channel_row_var = tk.IntVar(value=self.config["channel_names_row"])
        ttk.Entry(area_frame, textvariable=self.channel_row_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5)
        
        # 预算设置
        budget_frame = ttk.LabelFrame(main_frame, text="3. 预算设置", padding="10")
        budget_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(budget_frame, text="总预算:").grid(row=0, column=0, sticky=tk.W)
        self.budget_var = tk.IntVar(value=self.config["total_budget"])
        ttk.Entry(budget_frame, textvariable=self.budget_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(budget_frame, text="优化方案数量:").grid(row=1, column=0, sticky=tk.W)
        self.schemes_var = tk.IntVar(value=self.config["optimization_schemes"])
        ttk.Entry(budget_frame, textvariable=self.schemes_var, width=5).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 基准方案设置
        ttk.Label(budget_frame, text="基准方案:").grid(row=2, column=0, sticky=tk.W)
        self.baseline_var = tk.StringVar(value=self.config["baseline_allocation"])
        baseline_combo = ttk.Combobox(budget_frame, textvariable=self.baseline_var, 
                                    values=["equal", "custom"], width=12)
        baseline_combo.grid(row=2, column=1, sticky=tk.W, padx=5)
        
        # 渠道约束设置
        constraint_frame = ttk.LabelFrame(main_frame, text="4. 渠道约束设置", padding="10")
        constraint_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(constraint_frame, text="读取渠道信息", 
                  command=self.load_channels).grid(row=0, column=0, pady=5)
        ttk.Button(constraint_frame, text="设置渠道约束", 
                  command=self.set_channel_constraints).grid(row=0, column=1, padx=5, pady=5)
        
        # 默认约束说明
        ttk.Label(constraint_frame, text="默认约束范围: 2%-60% (可通过设置渠道约束修改)",
                 foreground="blue").grid(row=0, column=2, padx=10)

        # 渠道信息显示
        self.channel_text = tk.Text(constraint_frame, height=8, width=70)
        self.channel_text.grid(row=1, column=0, columnspan=2, pady=5)
        
        scrollbar = ttk.Scrollbar(constraint_frame, orient="vertical", command=self.channel_text.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.channel_text.configure(yscrollcommand=scrollbar.set)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="保存配置", 
                  command=self.save_config).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="加载配置", 
                  command=self.load_config_file).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="开始优化", 
                  command=self.start_optimization).grid(row=0, column=2, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=6, column=0, columnspan=2)
    
    def browse_file(self):
        """浏览选择Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
    
    def load_channels(self):
        """读取渠道信息"""
        try:
            excel_file = self.file_var.get()
            if not excel_file or not os.path.exists(excel_file):
                messagebox.showerror("错误", "请先选择有效的Excel文件")
                return
            
            self.status_var.set("正在读取渠道信息...")
            
            # 初始化Excel
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False
            
            try:
                workbook = excel.Workbooks.Open(os.path.abspath(excel_file))
                worksheet = workbook.Worksheets(self.worksheet_var.get())
                
                # 读取渠道名称
                self.channels = []
                start_col = self.start_col_var.get()
                end_col = self.end_col_var.get()
                channel_row = self.channel_row_var.get()
                
                for col in range(start_col, end_col + 1):
                    cell_value = worksheet.Cells(channel_row, col).Value
                    if cell_value:
                        self.channels.append(str(cell_value).strip())
                
                # 显示渠道信息
                self.channel_text.delete(1.0, tk.END)
                self.channel_text.insert(tk.END, f"成功读取到 {len(self.channels)} 个媒体渠道:\n\n")
                for i, channel in enumerate(self.channels, 1):
                    constraint_info = ""
                    if channel in self.config["channel_constraints"]:
                        constraint = self.config["channel_constraints"][channel]
                        constraint_info = f" [约束: {constraint['min']:.1%}-{constraint['max']:.1%}]"
                    
                    self.channel_text.insert(tk.END, f"{i:2d}. {channel}{constraint_info}\n")
                
                self.status_var.set(f"成功读取 {len(self.channels)} 个渠道")
                
            finally:
                workbook.Close(SaveChanges=False)
                excel.Quit()
                
        except Exception as e:
            messagebox.showerror("错误", f"读取渠道信息失败: {str(e)}")
            self.status_var.set("读取失败")
    
    def set_channel_constraints(self):
        """设置渠道约束"""
        if not self.channels:
            messagebox.showwarning("警告", "请先读取渠道信息")
            return
        
        # 创建约束设置窗口
        constraint_window = tk.Toplevel(self.root)
        constraint_window.title("设置渠道约束")
        constraint_window.geometry("600x500")
        
        # 创建表格
        columns = ("渠道名称", "最小比例(%)", "最大比例(%)")
        tree = ttk.Treeview(constraint_window, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # 添加渠道数据
        for channel in self.channels:
            if channel in self.config["channel_constraints"]:
                constraint = self.config["channel_constraints"][channel]
                min_pct = constraint["min"] * 100
                max_pct = constraint["max"] * 100
            else:
                # 使用默认约束范围 2%-60%
                min_pct = 2.0
                max_pct = 60.0

            tree.insert("", tk.END, values=(channel, f"{min_pct:.1f}", f"{max_pct:.1f}"))
        
        tree.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        # 编辑功能
        def edit_constraint():
            selected = tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请选择要编辑的渠道")
                return
            
            item = tree.item(selected[0])
            channel = item["values"][0]
            
            # 创建编辑对话框
            edit_window = tk.Toplevel(constraint_window)
            edit_window.title(f"编辑约束 - {channel}")
            edit_window.geometry("300x150")
            
            ttk.Label(edit_window, text=f"渠道: {channel}").pack(pady=5)
            
            ttk.Label(edit_window, text="最小比例(%):").pack()
            min_var = tk.DoubleVar(value=float(item["values"][1]))
            ttk.Entry(edit_window, textvariable=min_var).pack(pady=2)
            
            ttk.Label(edit_window, text="最大比例(%):").pack()
            max_var = tk.DoubleVar(value=float(item["values"][2]))
            ttk.Entry(edit_window, textvariable=max_var).pack(pady=2)
            
            def save_constraint():
                min_val = min_var.get() / 100
                max_val = max_var.get() / 100
                
                if min_val < 0 or max_val > 1 or min_val > max_val:
                    messagebox.showerror("错误", "约束值无效")
                    return
                
                self.config["channel_constraints"][channel] = {
                    "min": min_val,
                    "max": max_val
                }
                
                # 更新树形视图
                tree.item(selected[0], values=(channel, f"{min_val*100:.1f}", f"{max_val*100:.1f}"))
                edit_window.destroy()
            
            ttk.Button(edit_window, text="保存", command=save_constraint).pack(pady=10)
        
        ttk.Button(constraint_window, text="编辑选中项", command=edit_constraint).pack(pady=5)
    
    def save_config(self):
        """保存配置"""
        self.config.update({
            "excel_file": self.file_var.get(),
            "worksheet_name": self.worksheet_var.get(),
            "input_start_row": self.start_row_var.get(),
            "input_end_row": self.end_row_var.get(),
            "input_start_col": self.start_col_var.get(),
            "input_end_col": self.end_col_var.get(),
            "output_col": self.output_col_var.get(),
            "channel_names_row": self.channel_row_var.get(),
            "total_budget": self.budget_var.get(),
            "optimization_schemes": self.schemes_var.get(),
            "baseline_allocation": self.baseline_var.get()
        })
        
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """加载默认配置"""
        config_file = "media_optimizer_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                    self.update_ui_from_config()
            except:
                pass
    
    def load_config_file(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                    self.update_ui_from_config()
                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
    
    def update_ui_from_config(self):
        """从配置更新界面"""
        self.file_var.set(self.config.get("excel_file", ""))
        self.worksheet_var.set(self.config.get("worksheet_name", "calculation"))
        self.start_row_var.set(self.config.get("input_start_row", 38))
        self.end_row_var.set(self.config.get("input_end_row", 57))
        self.start_col_var.set(self.config.get("input_start_col", 3))
        self.end_col_var.set(self.config.get("input_end_col", 18))
        self.output_col_var.set(self.config.get("output_col", 21))
        self.channel_row_var.set(self.config.get("channel_names_row", 1))
        self.budget_var.set(self.config.get("total_budget", 1000000))
        self.schemes_var.set(self.config.get("optimization_schemes", 5))
        self.baseline_var.set(self.config.get("baseline_allocation", "equal"))
    
    def start_optimization(self):
        """开始优化"""
        # 验证输入
        if not self.file_var.get() or not os.path.exists(self.file_var.get()):
            messagebox.showerror("错误", "请选择有效的Excel文件")
            return
        
        if not self.channels:
            messagebox.showerror("错误", "请先读取渠道信息")
            return
        
        # 在新线程中运行优化
        self.progress.start()
        self.status_var.set("正在优化...")
        
        optimization_thread = threading.Thread(target=self.run_optimization)
        optimization_thread.daemon = True
        optimization_thread.start()
    
    def run_optimization(self):
        """运行优化（在后台线程中）"""
        try:
            # 这里调用优化算法
            # 为了演示，我们创建一个简化的优化器
            from universal_optimizer_engine import UniversalOptimizerEngine
            
            engine = UniversalOptimizerEngine(self.config, self.channels)
            results = engine.optimize()
            
            # 在主线程中更新UI
            self.root.after(0, self.optimization_complete, results)
            
        except Exception as e:
            self.root.after(0, self.optimization_error, str(e))
    
    def optimization_complete(self, results):
        """优化完成"""
        self.progress.stop()
        self.status_var.set("优化完成")
        messagebox.showinfo("成功", f"优化完成！生成了 {len(results)} 个方案")
    
    def optimization_error(self, error_msg):
        """优化出错"""
        self.progress.stop()
        self.status_var.set("优化失败")
        messagebox.showerror("错误", f"优化失败: {error_msg}")


if __name__ == "__main__":
    root = tk.Tk()
    app = UniversalMediaOptimizer(root)
    root.mainloop()
