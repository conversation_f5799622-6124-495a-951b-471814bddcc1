#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的通用媒体预算优化工具
包含以下改进:
1. 修复Excel连接问题
2. 增加迭代次数提高uplift
3. 可视化进程
4. 美化报告
5. 实际约束范围显示
"""

import os
import time
from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

def test_improved_optimizer():
    """测试改进后的优化器"""
    
    # 检查Excel文件
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ 找不到Excel文件: {excel_file}")
        return False
    
    print("🚀 测试改进后的媒体预算优化工具")
    print("=" * 60)
    
    # 配置参数（包含混合约束）
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,
        "input_end_col": 18,
        "output_col": 21,
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 5,  # 生成5个方案
        "baseline_allocation": "equal",
        "custom_baseline_ratios": {},
        "channel_constraints": {
            # 测试混合约束：部分渠道自定义，部分使用默认
            "SearchVolume": {"min": 0.05, "max": 0.50},  # 搜索：5%-50%
            "DouyinKOL": {"min": 0.02, "max": 0.25},     # 抖音KOL：2%-25%
            "RedKOL": {"min": 0.02, "max": 0.20},        # 小红书KOL：2%-20%
            # 其他渠道使用默认2%-60%
        }
    }
    
    # 模拟渠道列表
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    try:
        print(f"📊 配置信息:")
        print(f"   - Excel文件: {excel_file}")
        print(f"   - 总预算: ¥{config['total_budget']:,}")
        print(f"   - 生成方案数: {config['optimization_schemes']}")
        print(f"   - 自定义约束渠道: {len(config['channel_constraints'])}个")
        print(f"   - 默认约束渠道: {len(channels) - len(config['channel_constraints'])}个")
        
        print(f"\n🔧 开始优化...")
        start_time = time.time()
        
        # 创建优化器
        optimizer = UniversalOptimizerEngineV2(config, channels)
        
        # 运行优化
        results = optimizer.optimize()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ 优化完成！")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print(f"📈 基准KPI: {results['baseline_kpi']:,.2f}")
        print(f"📋 生成方案数: {len(results['results'])}")
        
        if results['output_file']:
            print(f"📄 报告文件: {results['output_file']}")
        
        # 显示优化结果摘要
        print(f"\n🏆 优化结果摘要:")
        print("-" * 60)
        
        for i, result in enumerate(results['results'][:5], 1):
            improvement_pct = result['提升比例']
            icon = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
            
            print(f"{icon} {i}. {result['方案名称']}")
            print(f"     KPI: {result['KPI']:,.2f} | 提升: {improvement_pct}")
        
        # 显示最优方案的渠道分配
        if len(results['results']) > 1:
            best_result = results['results'][0] if results['results'][0]['方案名称'] != "基准方案" else results['results'][1]
            
            print(f"\n🎯 最优方案详情: {best_result['方案名称']}")
            print("-" * 40)
            
            # 按预算排序显示前5个渠道
            budget_items = [(ch, budget) for ch, budget in best_result['预算分配'].items()]
            budget_items.sort(key=lambda x: x[1], reverse=True)
            
            print("前5个渠道预算分配:")
            for i, (channel, budget) in enumerate(budget_items[:5], 1):
                ratio = budget / config['total_budget']
                print(f"  {i}. {channel}: {ratio:>6.1%} (¥{budget:>8,.0f})")
        
        # 显示约束效果
        print(f"\n🔒 约束效果验证:")
        constraint_summary = optimizer.get_constraint_summary()
        print(f"   {constraint_summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_baseline():
    """测试自定义基准方案"""
    
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ 找不到Excel文件: {excel_file}")
        return False
    
    print(f"\n🎨 测试自定义基准方案功能")
    print("=" * 60)
    
    # 自定义基准配比（重点投放数字媒体）
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,
        "input_end_col": 18,
        "output_col": 21,
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 3,
        "baseline_allocation": "custom",
        "custom_baseline_ratios": {
            "SearchVolume": 25.0,    # 搜索25%
            "DouyinKOL": 20.0,       # 抖音KOL 20%
            "OTTPreroll": 15.0,      # OTT视频15%
            "Display": 10.0,         # 展示广告10%
            "RedKOL": 8.0,           # 小红书KOL 8%
            "RTBOCPX": 7.0,          # RTB 7%
            "DouyinPush": 5.0,       # 抖音推广5%
            "WeiboKOL": 3.0,         # 微博KOL 3%
            "OTVPreroll": 2.0,       # OTV视频2%
            "DigitalCoop": 2.0,      # 数字合作2%
            "BuildingLCD": 1.0,      # 楼宇LCD 1%
            "Metro": 1.0,            # 地铁1%
            "CreativeOutdoor": 0.5,  # 创意户外0.5%
            "SponsorEvent": 0.3,     # 赞助活动0.3%
            "SponsorDrama": 0.1,     # 赞助剧集0.1%
            "WeiboPush": 0.1         # 微博推广0.1%
        },
        "channel_constraints": {}
    }
    
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    try:
        print(f"📊 自定义基准配比（数字媒体重点）:")
        total_check = sum(config['custom_baseline_ratios'].values())
        print(f"   配比总和: {total_check:.1f}%")
        
        # 显示主要渠道配比
        sorted_ratios = sorted(config['custom_baseline_ratios'].items(), 
                              key=lambda x: x[1], reverse=True)
        for channel, ratio in sorted_ratios[:5]:
            print(f"   {channel}: {ratio:.1f}%")
        
        print(f"\n🔧 开始优化...")
        optimizer = UniversalOptimizerEngineV2(config, channels)
        results = optimizer.optimize()
        
        print(f"\n✅ 自定义基准测试成功！")
        print(f"📈 基准KPI: {results['baseline_kpi']:,.2f}")
        
        # 显示优化效果
        best_result = results['results'][0]
        if best_result['方案名称'] != "基准方案（自定义配比）":
            improvement = best_result['提升比例']
            print(f"🎯 最优方案提升: {improvement}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义基准测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试改进后的优化工具...")
    
    # 测试1: 基本功能（包含混合约束）
    success1 = test_improved_optimizer()
    
    # 测试2: 自定义基准方案
    success2 = test_custom_baseline()
    
    print(f"\n" + "=" * 60)
    if success1 and success2:
        print(f"🎉 所有测试通过！改进功能验证成功！")
        print(f"\n✨ 主要改进:")
        print(f"   ✅ 修复Excel连接问题")
        print(f"   ✅ 增加迭代次数（500+次）提高uplift")
        print(f"   ✅ 添加进度可视化")
        print(f"   ✅ 美化Excel报告格式")
        print(f"   ✅ 显示实际约束范围")
        print(f"   ✅ 支持混合约束设置")
        
        print(f"\n🚀 工具已准备就绪，可以使用:")
        print(f"   python 启动通用优化工具_v2.py")
    else:
        print(f"⚠️  部分测试失败，请检查配置")
    
    input(f"\n按回车键退出...")
