#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能方案描述系统
1. 通用渠道分类（可适配不同项目）
2. 基于媒体比例的业务描述
3. 结合广告行业的专业术语
"""

import os
from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

def test_channel_classification():
    """测试通用渠道分类系统"""
    print("🧪 测试通用渠道分类系统")
    print("=" * 50)
    
    # 测试不同项目的渠道列表
    test_projects = [
        {
            "name": "Gatorade项目",
            "channels": [
                "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
                "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
                "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
                "DouyinPush", "WeiboPush"
            ]
        },
        {
            "name": "电商项目",
            "channels": [
                "TmallSearch", "JDDisplay", "TaobaoKOL", "WeChatMoments", 
                "AppStore", "GoogleAds", "FacebookAds", "InstagramKOL",
                "YouTubeVideo", "TikTokAds", "AmazonSponsor", "ShopeeDisplay"
            ]
        },
        {
            "name": "汽车项目", 
            "channels": [
                "AutoShow", "SportsSponsorship", "CinemaAds", "RadioAds",
                "BaiduSEM", "WeiboDisplay", "AutoHomeContent", "BitAutoKOL",
                "OutdoorBillboard", "MetroAds", "TVCommercial", "MagazineAds"
            ]
        }
    ]
    
    for project in test_projects:
        print(f"\n📊 {project['name']} 渠道分类:")
        print("-" * 40)
        
        # 创建模拟优化器（添加必要的配置）
        config = {
            "total_budget": 1000000,
            "excel_file": "dummy.xlsx",  # 模拟文件
            "worksheet_name": "test",
            "input_start_row": 1,
            "input_end_row": 10,
            "input_start_col": 1,
            "input_end_col": 10,
            "output_col": 11,
            "output_start_row": 1,
            "output_end_row": 10,
            "channel_names_row": 1,
            "optimization_schemes": 3,
            "max_iterations": 100,
            "baseline_allocation": "equal",
            "custom_baseline_ratios": {},
            "channel_constraints": {}
        }
        optimizer = UniversalOptimizerEngineV2(config, project['channels'])
        
        # 获取渠道分类
        channel_classification, categories = optimizer.get_channel_categories()
        
        for category, channels_in_cat in channel_classification.items():
            if channels_in_cat:  # 只显示有渠道的分类
                description = categories.get(category, {}).get("description", "")
                print(f"  {category} ({description}):")
                for channel in channels_in_cat:
                    print(f"    - {channel}")
        
        print(f"  总计: {len(project['channels'])} 个渠道，{len(channel_classification)} 个分类")
    
    print(f"\n✅ 通用渠道分类系统测试完成")
    return True

def test_smart_descriptions():
    """测试智能方案描述生成"""
    print(f"\n🧪 测试智能方案描述生成")
    print("=" * 50)
    
    # Gatorade项目渠道
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    config = {
        "total_budget": 1000000,
        "excel_file": "dummy.xlsx",
        "worksheet_name": "test",
        "input_start_row": 1,
        "input_end_row": 10,
        "input_start_col": 1,
        "input_end_col": 10,
        "output_col": 11,
        "output_start_row": 1,
        "output_end_row": 10,
        "channel_names_row": 1,
        "optimization_schemes": 3,
        "max_iterations": 100,
        "baseline_allocation": "equal",
        "custom_baseline_ratios": {},
        "channel_constraints": {}
    }
    optimizer = UniversalOptimizerEngineV2(config, channels)
    
    # 测试不同的预算分配策略
    test_scenarios = [
        {
            "name": "基准方案（平均分配）",
            "allocation": {ch: 62500 for ch in channels}  # 平均分配
        },
        {
            "name": "数字化主导方案",
            "allocation": {
                "SearchVolume": 250000, "Display": 150000, "RTBOCPX": 100000,
                "DouyinKOL": 120000, "RedKOL": 80000, "WeiboKOL": 60000,
                "OTTPreroll": 80000, "OTVPreroll": 40000,
                "BuildingLCD": 30000, "Metro": 20000, "CreativeOutdoor": 20000,
                "SponsorEvent": 25000, "SponsorDrama": 15000,
                "DouyinPush": 0, "WeiboPush": 0, "DigitalCoop": 0
            }
        },
        {
            "name": "社交媒体重点方案",
            "allocation": {
                "DouyinKOL": 200000, "RedKOL": 150000, "WeiboKOL": 100000,
                "DouyinPush": 80000, "WeiboPush": 50000,
                "SearchVolume": 120000, "Display": 80000, "RTBOCPX": 60000,
                "OTTPreroll": 50000, "OTVPreroll": 30000,
                "BuildingLCD": 25000, "Metro": 20000, "CreativeOutdoor": 15000,
                "SponsorEvent": 10000, "SponsorDrama": 0, "DigitalCoop": 0
            }
        },
        {
            "name": "传统媒体主导方案",
            "allocation": {
                "OTTPreroll": 180000, "OTVPreroll": 120000,
                "BuildingLCD": 150000, "Metro": 100000, "CreativeOutdoor": 80000,
                "SponsorEvent": 100000, "SponsorDrama": 70000,
                "SearchVolume": 80000, "Display": 50000, "RTBOCPX": 30000,
                "DouyinKOL": 20000, "RedKOL": 10000, "WeiboKOL": 5000,
                "DouyinPush": 0, "WeiboPush": 0, "DigitalCoop": 5000
            }
        },
        {
            "name": "KOL营销专注方案",
            "allocation": {
                "DouyinKOL": 300000, "RedKOL": 200000, "WeiboKOL": 150000,
                "SponsorEvent": 100000, "SponsorDrama": 80000,
                "SearchVolume": 60000, "Display": 40000, "RTBOCPX": 30000,
                "OTTPreroll": 20000, "OTVPreroll": 10000,
                "BuildingLCD": 5000, "Metro": 3000, "CreativeOutdoor": 2000,
                "DouyinPush": 0, "WeiboPush": 0, "DigitalCoop": 0
            }
        }
    ]
    
    print("方案描述测试结果:")
    print("-" * 60)
    
    for scenario in test_scenarios:
        description = optimizer.generate_scheme_description(
            scenario["name"], 
            scenario["allocation"]
        )
        
        # 计算主要渠道类型占比
        channel_classification, _ = optimizer.get_channel_categories()
        category_ratios = {}
        for category, channels_in_cat in channel_classification.items():
            total_budget_in_cat = sum(scenario["allocation"].get(ch, 0) for ch in channels_in_cat)
            ratio = total_budget_in_cat / config["total_budget"]
            category_ratios[category] = ratio
        
        print(f"\n📋 {scenario['name']}")
        print(f"   描述: {description}")
        print(f"   分类占比:")
        for category, ratio in sorted(category_ratios.items(), key=lambda x: x[1], reverse=True):
            if ratio > 0.05:  # 只显示占比超过5%的分类
                print(f"     {category}: {ratio:.1%}")
    
    print(f"\n✅ 智能方案描述测试完成")
    return True

def test_real_optimization_with_descriptions():
    """测试真实优化中的智能描述"""
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"\n⚠️  跳过真实优化测试：找不到Excel文件 {excel_file}")
        return True
    
    print(f"\n🧪 测试真实优化中的智能描述")
    print("=" * 50)
    
    # 配置参数
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,
        "input_end_col": 18,
        "output_col": 21,
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 3,
        "max_iterations": 30,  # 减少迭代次数
        "baseline_allocation": "equal",
        "custom_baseline_ratios": {},
        "channel_constraints": {
            "SearchVolume": {"min": 0.05, "max": 0.40},
            "DouyinKOL": {"min": 0.02, "max": 0.25},
        }
    }
    
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    try:
        print(f"📊 开始优化测试（重点关注方案描述）...")
        
        def progress_callback(progress, status, detail=""):
            if progress % 50 == 0 or progress >= 95:
                print(f"  进度: {progress:3.0f}% | {status}")
                if detail:
                    print(f"         {detail}")
        
        optimizer = UniversalOptimizerEngineV2(config, channels, progress_callback)
        results = optimizer.optimize()
        
        print(f"\n📋 生成的方案及其智能描述:")
        print("-" * 60)
        
        for i, result in enumerate(results['results'], 1):
            print(f"\n{i}. {result['方案名称']} (KPI: {result['KPI']:,.0f}, 提升: {result['提升比例']})")
            print(f"   📝 描述: {result['方案描述']}")
            
            # 显示主要渠道分配
            top_channels = sorted(result['预算分配'].items(), key=lambda x: x[1], reverse=True)[:3]
            print(f"   💰 主要投入:")
            for channel, budget in top_channels:
                ratio = budget / config['total_budget']
                print(f"      {channel}: {ratio:.1%} (¥{budget:,.0f})")
        
        print(f"\n✅ 真实优化智能描述测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 真实优化测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试智能方案描述系统")
    print("=" * 60)
    
    tests = [
        ("通用渠道分类", test_channel_classification),
        ("智能方案描述", test_smart_descriptions),
        ("真实优化描述", test_real_optimization_with_descriptions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print(f"🎉 智能方案描述系统验证成功！")
        print(f"\n✨ 新功能特点:")
        print(f"   ✅ 通用渠道分类（支持任意项目）")
        print(f"   ✅ 基于媒体比例的智能描述")
        print(f"   ✅ 结合广告行业的专业术语")
        print(f"   ✅ 自动识别投放策略特点")
        print(f"   ✅ 适配不同行业和项目")
    else:
        print(f"⚠️  部分测试失败，请检查相关功能")
    
    input(f"\n按回车键退出...")
