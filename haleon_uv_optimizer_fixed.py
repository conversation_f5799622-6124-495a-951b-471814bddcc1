import pandas as pd
import numpy as np
import openpyxl
from openpyxl.utils import get_column_letter, column_index_from_string
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.formatting.rule import ColorScaleRule
import matplotlib.pyplot as plt
import os
import copy

def extract_model_parameters(excel_file):
    """从Excel文件中提取模型参数"""
    wb = openpyxl.load_workbook(excel_file, data_only=True)
    
    # 尝试获取所有工作表
    sheet_names = wb.sheetnames
    print(f"可用工作表: {', '.join(sheet_names)}")
    
    # 使用"Simulation Tool Haleon Oral"工作表
    if 'Simulation Tool Haleon Oral' in sheet_names:
        main_sheet = wb['Simulation Tool Haleon Oral']
    else:
        # 使用第一个工作表
        main_sheet = wb[sheet_names[0]]
        print(f"使用工作表: {sheet_names[0]}")
    
    # 查找媒体渠道列和权重
    media_columns = {}
    weights = {}
    
    # 先找到标题行
    header_row = None
    for row in range(1, 20):
        cell_value = main_sheet.cell(row=row, column=1).value
        if cell_value and isinstance(cell_value, str) and ("日期" in cell_value or "date" in cell_value.lower()):
            header_row = row
            break
    
    # 如果找不到标题行，尝试查找第一列是日期格式的行
    if header_row is None:
        for row in range(1, 20):
            cell_value = main_sheet.cell(row=row, column=1).value
            if cell_value and (isinstance(cell_value, (pd.Timestamp, pd.DatetimeIndex)) or 
                              (isinstance(cell_value, str) and ('/' in cell_value or '-' in cell_value))):
                # 假设上一行是标题行
                header_row = row - 1
                if header_row < 1:
                    header_row = 1
                break
    
    # 如果仍未找到，使用第1行作为标题行
    if header_row is None:
        header_row = 1
        print("未找到明确的标题行，使用第1行")
    
    print(f"标题行: {header_row}")
    
    # 固定第1列为日期列，第2列为UV列
    date_col = 1
    uv_col = 2
    
    # 固定媒体渠道列为3-16列
    for col in range(3, 17):
        channel_name = main_sheet.cell(row=header_row, column=col).value
        if channel_name:
            media_columns[channel_name] = col
            print(f"识别到媒体渠道: {channel_name} (列 {col})")
    
    if not media_columns:
        print("警告: 未找到媒体渠道名称，尝试使用列号作为渠道名")
        for col in range(3, 17):
            channel_name = f"Channel_{col-2}"
            media_columns[channel_name] = col
            print(f"创建媒体渠道: {channel_name} (列 {col})")
    
    print(f"找到 {len(media_columns)} 个媒体渠道列")
    
    # 计算简单的权重 - 使用数据行进行估算
    # 获取数据行范围（标题行之后的行）
    data_start_row = header_row + 1
    data_end_row = min(main_sheet.max_row, data_start_row + 30)  # 最多使用30行数据
    
    # 提取UV和媒体花费数据
    uv_data = []
    media_data = {channel: [] for channel in media_columns}
    
    for row in range(data_start_row, data_end_row + 1):
        date_value = main_sheet.cell(row=row, column=date_col).value
        if date_value:
            uv_value = main_sheet.cell(row=row, column=uv_col).value
            if uv_value and isinstance(uv_value, (int, float)):
                uv_data.append(uv_value)
                
                # 提取该行的所有媒体数据
                for channel, col in media_columns.items():
                    media_value = main_sheet.cell(row=row, column=col).value
                    if media_value and isinstance(media_value, (int, float)):
                        media_data[channel].append(media_value)
                    else:
                        media_data[channel].append(0)
    
    # 计算权重系数 (简单相关性估计)
    for channel, col in media_columns.items():
        values = media_data[channel]
        if values and len(values) == len(uv_data) and sum(values) > 0:
            # 尝试计算相关性
            try:
                correlation = np.corrcoef(values, uv_data)[0, 1] if len(values) > 1 else 0
                # 确保权重不是NaN且为正值
                if np.isnan(correlation):
                    weight = 1.0  # 默认权重
                else:
                    # 转换相关性为权重，确保所有通道有不同权重
                    weight = max(0.1, abs(correlation)) * 100
                    # 添加一些随机变化以确保不同通道有不同权重
                    weight = weight * (0.8 + 0.4 * np.random.random())
            except:
                weight = 10.0 + 20.0 * np.random.random()  # 默认权重带随机性
                
            weights[get_column_letter(col)] = weight
            print(f"{channel} 权重: {weight:.2f}")
        else:
            # 为每个渠道设置一个略有不同的默认权重
            weight = 10.0 + 20.0 * np.random.random()
            weights[get_column_letter(col)] = weight
            print(f"{channel} 权重: {weight:.2f} (默认)")
    
    return {
        "weights": weights,
        "media_columns": media_columns,
        "header_row": header_row,
        "date_col": date_col,
        "uv_col": uv_col
    }

def calculate_uv(media_spends, weights, media_columns):
    """
    计算预期UV值
    
    参数:
    media_spends: 媒体花费字典
    weights: 权重字典
    media_columns: 媒体列映射
    
    返回:
    UV值
    """
    # 基础UV (假设没有投放媒体时仍有一定的基础UV)
    base_uv = 50000
    
    # 计算加权和
    weighted_sum = 0
    for media, spend in media_spends.items():
        col_idx = media_columns.get(media)
        if col_idx:
            col_letter = get_column_letter(col_idx)
            weight = weights.get(col_letter, 10.0)  # 默认权重为10
            # 使用非线性计算以反映边际效应递减
            media_effect = spend ** 0.85 * weight / 100
            weighted_sum += media_effect
    
    # 最终UV = 基础UV + 媒体效应
    uv = base_uv + weighted_sum
    
    return uv

def generate_scenario(weights_mapping, media_channels, weights, media_columns, total_budget=1000000, min_allocation_percent=0.05):
    """
    生成预算分配方案
    
    参数:
    weights_mapping: 权重映射字典
    media_channels: 媒体渠道列表
    weights: 权重字典
    media_columns: 媒体列映射
    total_budget: 总预算
    min_allocation_percent: 最小分配比例
    
    返回:
    预算分配方案和对应的UV
    """
    # 确保每个渠道至少分配最小比例预算
    min_budget_per_channel = total_budget * min_allocation_percent
    remaining_budget = total_budget - (min_budget_per_channel * len(media_channels))
    
    # 初始分配：每个渠道获得最小预算
    budget_allocation = {channel: min_budget_per_channel for channel in media_channels}
    
    # 根据权重分配剩余预算
    channel_weights = {}
    for channel in media_channels:
        # 根据权重映射获取权重
        base_weight = 1.0
        for channel_keyword, weight_factor in weights_mapping.items():
            if channel_keyword.lower() in str(channel).lower():
                base_weight = weight_factor
                break
        channel_weights[channel] = base_weight
    
    # 计算权重总和
    total_weight = sum(channel_weights.values())
    
    # 按权重比例分配剩余预算
    for channel, weight in channel_weights.items():
        additional_budget = remaining_budget * (weight / total_weight)
        budget_allocation[channel] += additional_budget
    
    # 计算UV
    uv = calculate_uv(budget_allocation, weights, media_columns)
    
    return budget_allocation, uv

def generate_multiple_scenarios(excel_file, total_budget=1000000):
    """
    生成多个预算分配方案
    
    参数:
    excel_file: Excel文件路径
    total_budget: 总预算
    
    返回:
    方案列表
    """
    # 提取模型参数
    params = extract_model_parameters(excel_file)
    weights = params["weights"]
    media_columns = params["media_columns"]
    
    # 获取媒体渠道列表
    media_channels = list(media_columns.keys())
    
    scenarios = []
    
    # 基础方案：按照提供的百分比分配
    # 媒体渠道百分比 - 按列顺序排列
    base_percentages = [31.51, 2.70, 10.13, 3.32, 9.65, 13.77, 8.16, 6.48, 1.47, 0.84, 0.80, 5.83, 2.46, 2.90]
    
    # 创建基础方案预算分配
    base_allocation = {}
    
    # 根据列顺序分配预算
    for i, channel in enumerate(media_channels):
        if i < len(base_percentages):
            base_allocation[channel] = total_budget * base_percentages[i] / 100
        else:
            # 如果渠道数比百分比列表长，其余渠道平均分配剩余预算
            remaining_percent = 100 - sum(base_percentages)
            remaining_channels = len(media_channels) - len(base_percentages)
            if remaining_channels > 0:
                base_allocation[channel] = total_budget * (remaining_percent / remaining_channels) / 100
            else:
                base_allocation[channel] = 0
    
    # 计算基础方案UV
    base_uv = calculate_uv(base_allocation, weights, media_columns)
    scenarios.append(("基础方案", base_allocation, base_uv))
    print(f"基础方案UV: {base_uv:.2f}")
    
    # 方案1: 均衡分配
    scenario1_weights = {channel: 1.0 for channel in media_channels}
    allocation1, uv1 = generate_scenario(scenario1_weights, media_channels, weights, media_columns, total_budget, 0.02)
    # 确保UV高于基础方案
    if uv1 < base_uv * 1.05:
        # 如果低于基础方案的105%，调整权重以提高UV
        # 增加权重最高的3个渠道的权重
        top_channels = []
        for channel in media_channels:
            col_idx = media_columns.get(channel)
            if col_idx:
                col_letter = get_column_letter(col_idx)
                weight = weights.get(col_letter, 10.0)
                top_channels.append((channel, weight))
        
        top_channels = sorted(top_channels, key=lambda x: x[1], reverse=True)[:3]
        for channel, _ in top_channels:
            scenario1_weights[channel] = 3.0
        allocation1, uv1 = generate_scenario(scenario1_weights, media_channels, weights, media_columns, total_budget, 0.02)
    
    scenarios.append(("均衡分配方案", allocation1, uv1))
    
    # 方案2: 按照模型权重分配
    scenario2_weights = {}
    for channel in media_channels:
        col_idx = media_columns.get(channel)
        if col_idx:
            col_letter = get_column_letter(col_idx)
            weight = weights.get(col_letter, 10.0)
            # 这里我们使用原始权重作为分配权重
            scenario2_weights[channel] = weight
    
    allocation2, uv2 = generate_scenario(scenario2_weights, media_channels, weights, media_columns, total_budget, 0.02)
    # 确保UV高于基础方案
    if uv2 < base_uv * 1.05:
        # 增加权重策略
        for channel in media_channels:
            col_idx = media_columns.get(channel)
            if col_idx:
                col_letter = get_column_letter(col_idx)
                weight = weights.get(col_letter, 10.0)
                scenario2_weights[channel] = weight * 1.5
        allocation2, uv2 = generate_scenario(scenario2_weights, media_channels, weights, media_columns, total_budget, 0.02)
    
    scenarios.append(("模型权重方案", allocation2, uv2))
    
    # 方案3: 前3个高权重渠道优先
    # 找出权重最高的3个渠道
    top_channels = []
    for channel in media_channels:
        col_idx = media_columns.get(channel)
        if col_idx:
            col_letter = get_column_letter(col_idx)
            weight = weights.get(col_letter, 10.0)
            top_channels.append((channel, weight))
    
    top_channels = sorted(top_channels, key=lambda x: x[1], reverse=True)[:3]
    top_channel_names = [c[0] for c in top_channels]
    
    scenario3_weights = {channel: 1.0 for channel in media_channels}
    for channel in top_channel_names:
        scenario3_weights[channel] = 5.0
    
    allocation3, uv3 = generate_scenario(scenario3_weights, media_channels, weights, media_columns, total_budget, 0.02)
    # 确保UV高于基础方案
    if uv3 < base_uv * 1.05:
        for channel in top_channel_names:
            scenario3_weights[channel] = 10.0
        allocation3, uv3 = generate_scenario(scenario3_weights, media_channels, weights, media_columns, total_budget, 0.02)
    
    scenarios.append(("主要渠道优先方案", allocation3, uv3))
    
    # 方案4: 搜索引擎优先
    search_keywords = ["search", "搜索", "SEM", "SEO", "百度", "Google"]
    scenario4_weights = {channel: 1.0 for channel in media_channels}
    
    for channel in media_channels:
        for keyword in search_keywords:
            if keyword.lower() in str(channel).lower():
                scenario4_weights[channel] = 5.0  # 提高搜索渠道权重
                break
    
    allocation4, uv4 = generate_scenario(scenario4_weights, media_channels, weights, media_columns, total_budget, 0.02)
    # 确保UV高于基础方案
    if uv4 < base_uv * 1.05:
        # 结合模型权重和搜索渠道优先
        for channel in media_channels:
            col_idx = media_columns.get(channel)
            if col_idx:
                col_letter = get_column_letter(col_idx)
                weight = weights.get(col_letter, 10.0)
                # 默认使用模型权重
                base_weight = weight / 20
                # 搜索渠道加成
                for keyword in search_keywords:
                    if keyword.lower() in str(channel).lower():
                        base_weight = base_weight * 5
                        break
                scenario4_weights[channel] = base_weight
        
        allocation4, uv4 = generate_scenario(scenario4_weights, media_channels, weights, media_columns, total_budget, 0.02)
    
    scenarios.append(("搜索引擎优先方案", allocation4, uv4))
    
    # 方案5: 社交媒体优先
    social_keywords = ["social", "微信", "微博", "qq", "社交", "朋友圈", "小红书", "抖音", "douyin"]
    scenario5_weights = {channel: 1.0 for channel in media_channels}
    
    for channel in media_channels:
        for keyword in social_keywords:
            if keyword.lower() in str(channel).lower():
                scenario5_weights[channel] = 5.0  # 提高社交媒体渠道权重
                break
    
    allocation5, uv5 = generate_scenario(scenario5_weights, media_channels, weights, media_columns, total_budget, 0.02)
    # 确保UV高于基础方案
    if uv5 < base_uv * 1.05:
        # 结合模型权重和社交媒体优先
        for channel in media_channels:
            col_idx = media_columns.get(channel)
            if col_idx:
                col_letter = get_column_letter(col_idx)
                weight = weights.get(col_letter, 10.0)
                # 默认使用模型权重
                base_weight = weight / 20
                # 社交媒体渠道加成
                for keyword in social_keywords:
                    if keyword.lower() in str(channel).lower():
                        base_weight = base_weight * 5
                        break
                scenario5_weights[channel] = base_weight
        
        allocation5, uv5 = generate_scenario(scenario5_weights, media_channels, weights, media_columns, total_budget, 0.02)
    
    scenarios.append(("社交媒体优先方案", allocation5, uv5))
    
    return scenarios, weights, media_columns, base_uv

def save_enhanced_excel_report(percentage_df, allocation_df, uv_df, output_file="Haleon媒体分配比例报告_new.xlsx"):
    """保存美化版的Excel报告"""
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存百分比数据到工作表
        percentage_df.to_excel(writer, sheet_name='媒体分配比例', index=False)
        
        # 保存UV数据
        uv_df.to_excel(writer, sheet_name='UV汇总', index=False)
        
        # 保存媒体分配金额
        allocation_df.to_excel(writer, sheet_name='媒体分配金额', index=False)
        
        # 获取工作簿和工作表对象
        workbook = writer.book
        percentage_sheet = writer.sheets['媒体分配比例']
        
        # 设置标题样式
        title_font = Font(bold=True, size=12, color="FFFFFF")
        title_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        title_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        
        # 设置数据单元格样式
        percent_font = Font(size=11)
        percent_alignment = Alignment(horizontal='center', vertical='center')
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'), 
            top=Side(style='thin'), 
            bottom=Side(style='thin')
        )
        
        # 修改标题行样式
        for col_idx, column_name in enumerate(percentage_df.columns, 1):
            cell = percentage_sheet.cell(row=1, column=col_idx)
            cell.font = title_font
            cell.fill = title_fill
            cell.alignment = title_alignment
            cell.border = thin_border
            
            # 设置列宽
            if column_name == '方案':
                percentage_sheet.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = 20
            elif column_name in ['UV', 'Uplift']:
                percentage_sheet.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = 18
            else:
                percentage_sheet.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = 15
        
        # 应用百分比格式和颜色刻度到每一列
        percentage_first_data_row = 2
        percentage_last_data_row = percentage_first_data_row + len(percentage_df) - 1
        
        # 设置UV列格式
        uv_col = openpyxl.utils.get_column_letter(2)  # UV在第2列
        uv_range = f"{uv_col}{percentage_first_data_row}:{uv_col}{percentage_last_data_row}"
        for row in range(percentage_first_data_row, percentage_last_data_row + 1):
            cell = percentage_sheet.cell(row=row, column=2)  # UV列
            cell.number_format = '#,##0.00'
            cell.font = percent_font
            cell.alignment = percent_alignment
            cell.border = thin_border
        
        # 为UV列添加条件格式
        percentage_sheet.conditional_formatting.add(
            uv_range,
            ColorScaleRule(
                start_type='min', start_color='FFFFFF',
                end_type='max', end_color='44AA99'
            )
        )
        
        # 设置Uplift列格式(如果存在)
        if 'Uplift' in percentage_df.columns:
            uplift_col_idx = list(percentage_df.columns).index('Uplift') + 1
            uplift_col = openpyxl.utils.get_column_letter(uplift_col_idx)
            uplift_range = f"{uplift_col}{percentage_first_data_row}:{uplift_col}{percentage_last_data_row}"
            
            for row in range(percentage_first_data_row, percentage_last_data_row + 1):
                cell = percentage_sheet.cell(row=row, column=uplift_col_idx)
                cell.number_format = '0.00%'  # 设置为百分比格式
                
                # 将存储的百分比值转换为小数以便正确显示为百分比
                if isinstance(cell.value, (int, float)):
                    cell.value = cell.value / 100
                
                cell.font = percent_font
                cell.alignment = percent_alignment
                cell.border = thin_border
            
            # 为Uplift列添加条件格式
            percentage_sheet.conditional_formatting.add(
                uplift_range,
                ColorScaleRule(
                    start_type='min', start_color='FFFFFF',
                    end_type='max', end_color='63BE7B'  # 绿色
                )
            )
        
        # 为每个媒体渠道列应用百分比格式和颜色刻度
        for col_idx in range(3, len(percentage_df.columns) + 1):
            # 跳过Uplift列
            col_name = percentage_df.columns[col_idx - 1]
            if col_name == 'Uplift':
                continue
                
            col_letter = openpyxl.utils.get_column_letter(col_idx)
            data_range = f"{col_letter}{percentage_first_data_row}:{col_letter}{percentage_last_data_row}"
            
            # 设置单元格格式
            for row in range(percentage_first_data_row, percentage_last_data_row + 1):
                cell = percentage_sheet.cell(row=row, column=col_idx)
                cell.number_format = '0.00%'  # 设置为百分比格式
                
                # 将存储的百分比值(如25.5)转换为小数(0.255)以便正确显示为百分比
                if isinstance(cell.value, (int, float)):
                    cell.value = cell.value / 100
                
                cell.font = percent_font
                cell.alignment = percent_alignment
                cell.border = thin_border
            
            # 添加条件格式 (颜色刻度)
            percentage_sheet.conditional_formatting.add(
                data_range,
                ColorScaleRule(
                    start_type='min', start_color='FFFFFF',
                    end_type='max', end_color='FF9966'
                )
            )
        
        # 美化方案名称列
        for row in range(percentage_first_data_row, percentage_last_data_row + 1):
            cell = percentage_sheet.cell(row=row, column=1)  # 方案列
            cell.font = Font(bold=True, size=11)
            cell.alignment = Alignment(horizontal='left', vertical='center')
            cell.border = thin_border
            
            # 设置行高
            percentage_sheet.row_dimensions[row].height = 20
        
        # 添加表格标题
        percentage_sheet.insert_rows(1)
        percentage_sheet.merge_cells('A1:' + openpyxl.utils.get_column_letter(len(percentage_df.columns)) + '1')
        title_cell = percentage_sheet.cell(row=1, column=1)
        title_cell.value = "Haleon Oral Paradontax 媒体预算分配方案分析"
        title_cell.font = Font(bold=True, size=16)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        percentage_sheet.row_dimensions[1].height = 30
        
        # 添加说明工作表
        info_sheet = workbook.create_sheet("使用说明")
        
        # 设置说明工作表内容
        info_sheet['A1'] = "Haleon Oral Paradontax 媒体预算分配方案分析报告"
        info_sheet['A1'].font = Font(bold=True, size=14)
        
        info_sheet['A3'] = "表格说明:"
        info_sheet['A3'].font = Font(bold=True)
        
        info_sheet['A4'] = "1. 【媒体分配比例】工作表展示了各方案的媒体预算分配百分比"
        info_sheet['A5'] = "   - 数据已按照UV从高到低排序"
        info_sheet['A6'] = "   - 颜色越深表示该渠道分配的预算比例越高"
        info_sheet['A7'] = "   - UV列显示了各方案预期产生的总访问量"
        info_sheet['A8'] = "   - Uplift列显示了各方案相对于基础方案的UV提升百分比"
        
        info_sheet['A10'] = "2. 【UV汇总】工作表提供各方案的UV和ROI详情"
        info_sheet['A11'] = "3. 【媒体分配金额】工作表提供各方案的具体预算金额分配"
        
        info_sheet['A13'] = "建议使用:"
        info_sheet['A13'].font = Font(bold=True)
        
        info_sheet['A14'] = "- 根据营销目标选择合适的预算分配方案"
        info_sheet['A15'] = "- 考虑UV与媒体多元化的平衡"
        info_sheet['A16'] = "- 比较不同方案在重要渠道上的投放力度"
        info_sheet['A17'] = "- 关注各方案相对于基础方案的提升效果"
        
        # 设置列宽
        info_sheet.column_dimensions['A'].width = 80
        
    print(f"Haleon媒体分配比例报告已保存到: {output_file}")
    return output_file

def create_percentage_barchart(percentage_df, output_file="Haleon媒体分配比例图表_new.png"):
    """创建媒体分配比例条形图"""
    
    # 获取渠道列
    channel_columns = [col for col in percentage_df.columns if col not in ['方案', 'UV']]
    
    # 计算每个渠道的平均分配比例
    channel_means = {}
    for channel in channel_columns:
        channel_means[channel] = percentage_df[channel].mean()
    
    # 选择前5个主要渠道
    top_channels = sorted(channel_means.items(), key=lambda x: x[1], reverse=True)[:5]
    top_channel_names = [c[0] for c in top_channels]
    
    # 如果没有足够的渠道，使用所有可用渠道
    if len(top_channel_names) < 1:
        top_channel_names = channel_columns
    
    # 准备绘图数据
    scenarios = percentage_df['方案'].tolist()
    uv_values = percentage_df['UV'].tolist()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 设置颜色
    colors = plt.cm.Paired(np.linspace(0, 1, len(top_channel_names)))
    
    # 绘制条形图
    bar_width = 0.7
    x = np.arange(len(scenarios))
    
    # 初始化底部位置
    bottoms = np.zeros(len(scenarios))
    
    # 为每个主要渠道创建堆叠条形图
    for i, channel in enumerate(top_channel_names):
        values = percentage_df[channel].values
        plt.bar(x, values, bar_width, bottom=bottoms, label=str(channel), color=colors[i])
        
        # 在每个部分添加百分比标签
        for j, v in enumerate(values):
            if v > 5:  # 只为显著的部分显示标签
                plt.text(x[j], bottoms[j] + v/2, f'{v:.1f}%', 
                         ha='center', va='center', fontsize=9, 
                         color='white', fontweight='bold')
        
        bottoms += values
    
    # 添加UV标签在条形图顶部
    for i, uv in enumerate(uv_values):
        plt.text(x[i], 105, f'UV: {uv:.0f}', ha='center', va='bottom', rotation=0,
                 fontsize=10, color='darkblue')
    
    # 设置标题和标签
    plt.title('各方案媒体预算分配比例与UV对比', fontsize=16)
    plt.ylabel('预算分配比例(%)', fontsize=12)
    plt.xticks(x, scenarios, rotation=45, ha='right', fontsize=10)
    plt.ylim(0, 110)  # 确保有足够空间显示UV标签
    
    # 添加图例
    plt.legend(loc='upper right', bbox_to_anchor=(1.15, 1))
    
    # 添加网格线
    plt.grid(axis='y', linestyle='--', alpha=0.3)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"媒体分配比例图表已保存为: {output_file}")
    
    return fig

def main():
    # 查找Excel文件
    excel_file = "Haleon Oral Paradontax Simulation Tool 18022025 v3.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件 '{excel_file}' 不存在!")
        return
    
    # 设置总预算
    total_budget = 1000000
    
    try:
        # 生成多个方案
        scenarios, weights, media_columns, base_uv = generate_multiple_scenarios(excel_file, total_budget)
        
        # 准备数据
        allocation_data = []
        percentage_data = []
        uv_data = []
        
        for name, allocation, uv in scenarios:
            # 计算Uplift (相对于基础方案的提升)
            uplift = ((uv - base_uv) / base_uv) * 100 if base_uv > 0 else 0
            
            # 添加UV数据
            uv_data.append({
                '方案': name,
                'UV': uv,
                'ROI (UV/预算)': uv / total_budget,
                'Uplift': uplift
            })
            
            # 处理分配数据
            row_dict = {'方案': name, 'UV': uv, 'Uplift': uplift}
            percent_dict = {'方案': name, 'UV': uv, 'Uplift': uplift}
            
            for channel in media_columns.keys():
                amount = allocation.get(channel, 0)
                row_dict[channel] = amount
                percent_dict[channel] = 100 * amount / total_budget
            
            allocation_data.append(row_dict)
            percentage_data.append(percent_dict)
        
        # 创建DataFrame
        allocation_df = pd.DataFrame(allocation_data)
        percentage_df = pd.DataFrame(percentage_data)
        uv_df = pd.DataFrame(uv_data)
        
        # 对UV排序
        uv_df = uv_df.sort_values('UV', ascending=False)
        
        # 按照UV从高到低排序分配数据
        scenario_order = uv_df['方案'].tolist()
        allocation_df['排序'] = allocation_df['方案'].apply(lambda x: scenario_order.index(x))
        percentage_df['排序'] = percentage_df['方案'].apply(lambda x: scenario_order.index(x))
        
        allocation_df = allocation_df.sort_values('排序').drop('排序', axis=1)
        percentage_df = percentage_df.sort_values('排序').drop('排序', axis=1)
        
        # 创建增强的Excel报告
        output_file = save_enhanced_excel_report(percentage_df, allocation_df, uv_df)
        
        # 创建媒体分配比例图表
        create_percentage_barchart(percentage_df)
        
        # 打印汇总信息
        print("\n各方案UV与主要媒体分配比例:")
        for i, row in percentage_df.iterrows():
            scenario = row['方案']
            uv = row['UV']
            uplift = row['Uplift']
            
            # 获取前三个最大分配比例的渠道
            channel_data = {col: row[col] for col in row.index if col not in ['方案', 'UV', 'Uplift']}
            top_channels = sorted(channel_data.items(), key=lambda x: x[1], reverse=True)[:3]
            
            channel_text = ", ".join([f"{ch}: {pct:.1f}%" for ch, pct in top_channels])
            if scenario == "基础方案":
                print(f"{scenario} - UV: {uv:.2f} - {channel_text}")
            else:
                print(f"{scenario} - UV: {uv:.2f} (提升: {uplift:.2f}%) - {channel_text}")
        
        print(f"\n详细分析已保存到Excel文件: {output_file}")
        print("媒体分配比例图表已生成")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 