#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的差异化方案测试
验证核心功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本差异化功能")
    print("=" * 50)
    
    try:
        from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
        
        # 简单配置
        channels = ["SearchVolume", "DouyinKOL", "OTTPreroll", "BuildingLCD"]
        config = {
            "excel_file": "dummy.xlsx",
            "worksheet_name": "test",
            "input_start_row": 1, "input_end_row": 10,
            "input_start_col": 1, "input_end_col": 10,
            "output_col": 11, "output_start_row": 1, "output_end_row": 10,
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 4,
            "max_iterations": 10,  # 减少迭代次数
            "baseline_allocation": "equal",
            "custom_baseline_ratios": {},
            "channel_constraints": {}
        }
        
        print(f"创建优化器...")
        optimizer = UniversalOptimizerEngineV2(config, channels)
        
        print(f"获取渠道分类...")
        channel_classification, _ = optimizer.get_channel_categories()
        
        print(f"渠道分类结果:")
        for category, channels_in_cat in channel_classification.items():
            if channels_in_cat:
                print(f"  {category}: {', '.join(channels_in_cat)}")
        
        # 模拟渠道表现
        channel_performance = [
            {'channel': 'SearchVolume', 'improvement': 1000, 'efficiency': 0.5},
            {'channel': 'DouyinKOL', 'improvement': 800, 'efficiency': 0.4},
            {'channel': 'OTTPreroll', 'improvement': 600, 'efficiency': 0.3},
            {'channel': 'BuildingLCD', 'improvement': 400, 'efficiency': 0.2}
        ]
        
        baseline_kpi = 10000
        
        print(f"\n生成差异化方案...")
        schemes = optimizer.generate_differentiated_schemes(baseline_kpi, channel_performance)
        
        print(f"\n生成的方案:")
        for i, scheme in enumerate(schemes, 1):
            print(f"  {i}. {scheme['方案名称']}: {scheme['提升比例']}")
            
            # 显示主要类别
            main_category = optimizer.get_scheme_main_category(scheme)
            print(f"     主要类别: {main_category}")
        
        # 验证差异化
        improvements = [float(s["提升比例"].rstrip('%')) for s in schemes]
        main_categories = [optimizer.get_scheme_main_category(s) for s in schemes]
        
        print(f"\n质量分析:")
        print(f"  提升范围: {min(improvements):.1f}% - {max(improvements):.1f}%")
        print(f"  独特类别: {len(set(cat for cat in main_categories if cat))}")
        
        # 验证递进性
        is_progressive = all(improvements[i] <= improvements[i+1] for i in range(len(improvements)-1))
        print(f"  递进趋势: {'✅ 正确' if is_progressive else '❌ 错误'}")
        
        print(f"\n✅ 基本差异化功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_generation():
    """测试策略生成"""
    print(f"\n🧪 测试策略生成")
    print("=" * 50)
    
    try:
        from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
        
        channels = ["SearchVolume", "DouyinKOL", "RedKOL", "OTTPreroll", "BuildingLCD", "SponsorEvent"]
        config = {
            "excel_file": "dummy.xlsx",
            "worksheet_name": "test",
            "input_start_row": 1, "input_end_row": 10,
            "input_start_col": 1, "input_end_col": 10,
            "output_col": 11, "output_start_row": 1, "output_end_row": 10,
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 5,
            "max_iterations": 10,
            "baseline_allocation": "equal",
            "custom_baseline_ratios": {},
            "channel_constraints": {}
        }
        
        optimizer = UniversalOptimizerEngineV2(config, channels)
        channel_classification, _ = optimizer.get_channel_categories()
        
        print(f"可用渠道类别:")
        available_categories = [cat for cat, channels in channel_classification.items() if channels]
        for cat in available_categories:
            print(f"  - {cat}")
        
        # 测试策略生成逻辑
        print(f"\n测试策略生成逻辑:")
        
        # 模拟策略生成
        strategies = []
        
        if "数字媒体" in available_categories or "社交媒体" in available_categories:
            strategies.append("数字营销重点方案")
        
        if "社交媒体" in available_categories:
            strategies.append("社交互动专注方案")
        
        if "视频广告" in available_categories:
            strategies.append("视频广告重点方案")
        
        if "线下媒体" in available_categories:
            strategies.append("线下媒体重点方案")
        
        if "赞助营销" in available_categories:
            strategies.append("赞助营销重点方案")
        
        print(f"可生成的策略:")
        for i, strategy in enumerate(strategies, 1):
            print(f"  {i}. {strategy}")
        
        print(f"\n策略数量: {len(strategies)}")
        print(f"目标方案数: {config['optimization_schemes'] - 1}")  # 减去基准方案
        
        print(f"\n✅ 策略生成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 策略生成测试失败: {str(e)}")
        return False

def test_budget_allocation():
    """测试预算分配"""
    print(f"\n🧪 测试预算分配")
    print("=" * 50)
    
    try:
        from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
        
        channels = ["SearchVolume", "DouyinKOL", "OTTPreroll"]
        config = {
            "excel_file": "dummy.xlsx",
            "worksheet_name": "test",
            "input_start_row": 1, "input_end_row": 10,
            "input_start_col": 1, "input_end_col": 10,
            "output_col": 11, "output_start_row": 1, "output_end_row": 10,
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 3,
            "max_iterations": 10,
            "baseline_allocation": "equal",
            "custom_baseline_ratios": {},
            "channel_constraints": {}
        }
        
        optimizer = UniversalOptimizerEngineV2(config, channels)
        
        channel_classification = {
            "数字媒体": ["SearchVolume"],
            "社交媒体": ["DouyinKOL"],
            "视频广告": ["OTTPreroll"]
        }
        
        channel_performance = [
            {'channel': 'SearchVolume', 'improvement': 1000, 'efficiency': 0.5},
            {'channel': 'DouyinKOL', 'improvement': 800, 'efficiency': 0.4},
            {'channel': 'OTTPreroll', 'improvement': 600, 'efficiency': 0.3}
        ]
        
        # 测试不同策略的预算分配
        test_strategy = {
            "name": "数字营销重点方案",
            "focus_categories": ["数字媒体"],
            "focus_ratio": 0.6,
            "target_improvement": 0.05
        }
        
        print(f"测试策略: {test_strategy['name']}")
        print(f"重点类别: {test_strategy['focus_categories']}")
        print(f"重点比例: {test_strategy['focus_ratio']:.0%}")
        
        budgets = optimizer.allocate_budget_by_strategy(test_strategy, channel_classification, channel_performance)
        
        print(f"\n预算分配结果:")
        total_budget = sum(budgets)
        for channel, budget in zip(channels, budgets):
            ratio = budget / total_budget
            is_focus = channel in channel_classification.get("数字媒体", [])
            marker = "🎯" if is_focus else "  "
            print(f"  {marker} {channel}: {ratio:.1%} (¥{budget:,.0f})")
        
        print(f"\n总预算验证: ¥{total_budget:,.0f} (目标: ¥{config['total_budget']:,.0f})")
        
        # 验证重点渠道比例
        focus_budget = budgets[0]  # SearchVolume
        focus_ratio = focus_budget / total_budget
        print(f"实际重点比例: {focus_ratio:.1%} (目标: {test_strategy['focus_ratio']:.0%})")
        
        print(f"\n✅ 预算分配测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 预算分配测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始简化的差异化方案测试")
    print("=" * 60)
    
    tests = [
        ("基本差异化功能", test_basic_functionality),
        ("策略生成", test_strategy_generation),
        ("预算分配", test_budget_allocation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print(f"🎉 差异化方案核心功能验证成功！")
        print(f"\n✨ 核心改进:")
        print(f"   ✅ 动态生成基于渠道类别的差异化策略")
        print(f"   ✅ 每种渠道类别都有对应的重点方案")
        print(f"   ✅ 根据目标提升调整预算分配强度")
        print(f"   ✅ 确保方案按提升幅度递进排列")
        print(f"   ✅ 避免方案同质化问题")
        print(f"\n💡 解决的具体问题:")
        print(f"   - 后三个方案很类似 → 每个方案重点不同的渠道类别")
        print(f"   - 提升不均匀递进 → 按目标提升幅度排序和选择")
        print(f"   - 缺乏差异化 → 基于渠道分类的差异化策略")
    else:
        print(f"⚠️  部分测试失败，请检查相关功能")
    
    input(f"\n按回车键退出...")
