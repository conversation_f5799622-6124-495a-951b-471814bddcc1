#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用媒体预算优化工具启动器 v2.0
作者: AI Assistant
版本: 2.0
日期: 2025-01-16

新功能:
- 支持输出行区域设置（可与输入区域不同）
- 支持自定义基准方案配比
- 优化的UI界面和用户体验
- 更详细的约束设置功能

使用说明:
1. 双击运行此文件启动图形界面
2. 或在命令行中运行: python 启动通用优化工具_v2.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import win32com.client
    except ImportError:
        missing_packages.append("pywin32")
    
    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")
    
    if missing_packages:
        error_msg = f"缺少以下依赖包: {', '.join(missing_packages)}\n\n"
        error_msg += "请运行以下命令安装:\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖包缺失", error_msg)
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("通用媒体预算优化工具 v2.0")
    print("=" * 60)
    print("新功能:")
    print("- 支持输出行区域设置")
    print("- 支持自定义基准方案配比")
    print("- 优化的UI界面")
    print("- 更详细的约束设置")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖包检查失败，程序退出")
        input("按回车键退出...")
        return
    
    # 检查必要文件
    required_files = [
        "universal_media_optimizer_v2.py",
        "universal_optimizer_engine_v2.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        error_msg = f"缺少以下必要文件: {', '.join(missing_files)}"
        print(f"错误: {error_msg}")
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动主程序
        from universal_media_optimizer_v2 import UniversalMediaOptimizerV2
        
        print("正在启动图形界面...")
        root = tk.Tk()
        app = UniversalMediaOptimizerV2(root)
        
        print("界面已启动，请在窗口中进行操作")
        print("\n主要功能:")
        print("1. 文件设置 - 选择Excel文件和工作表")
        print("2. 数据区域设置 - 配置输入和输出区域")
        print("3. 预算设置 - 设置总预算和基准方案")
        print("4. 渠道约束设置 - 设置各渠道预算限制")
        print("5. 开始优化 - 生成优化方案和分析报告")
        
        root.mainloop()
        
    except Exception as e:
        error_msg = f"启动失败: {str(e)}"
        print(f"错误: {error_msg}")
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", error_msg)
        
        input("按回车键退出...")

if __name__ == "__main__":
    main()
