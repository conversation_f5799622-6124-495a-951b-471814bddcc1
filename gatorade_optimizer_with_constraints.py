import pandas as pd
import numpy as np
import openpyxl
import win32com.client
import os
import time
from datetime import datetime
import itertools

class GatoradeConstrainedOptimizer:
    def __init__(self, excel_path):
        self.excel_path = os.path.abspath(excel_path)
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.channels = []
        self.total_budget = 1000000
        self.start_row = 38
        self.end_row = 57
        self.input_cols = list(range(3, 19))  # C到R列
        self.output_col = 21  # U列
        
        # 默认约束范围：2%-60%
        self.default_min_ratio = 0.02
        self.default_max_ratio = 0.60
        
        # 定义不同的投放策略和对应的渠道
        self.strategies = {
            "数字营销主导": {
                "description": "以数字化渠道为主，注重精准投放和数据驱动",
                "primary_channels": ["SearchVolume", "Display", "RTBOCPX", "DigitalCoop"],
                "secondary_channels": ["OTTPreroll", "OTVPreroll"],
                "support_channels": ["DouyinPush", "WeiboPush"],
                "primary_ratio": 0.6,
                "secondary_ratio": 0.25,
                "support_ratio": 0.15
            },
            "社交媒体主导": {
                "description": "以社交媒体和KOL为主，注重品牌影响力和用户互动",
                "primary_channels": ["DouyinKOL", "RedKOL", "WeiboKOL"],
                "secondary_channels": ["DouyinPush", "WeiboPush"],
                "support_channels": ["SearchVolume", "Display"],
                "primary_ratio": 0.5,
                "secondary_ratio": 0.3,
                "support_ratio": 0.2
            },
            "视频内容主导": {
                "description": "以视频广告为主，注重品牌故事传播和视觉冲击",
                "primary_channels": ["OTTPreroll", "OTVPreroll"],
                "secondary_channels": ["DouyinKOL", "DouyinPush"],
                "support_channels": ["SearchVolume", "Display", "RTBOCPX"],
                "primary_ratio": 0.45,
                "secondary_ratio": 0.35,
                "support_ratio": 0.2
            },
            "线下体验主导": {
                "description": "以线下媒体和体验营销为主，注重品牌认知和线下转化",
                "primary_channels": ["BuildingLCD", "Metro", "CreativeOutdoor"],
                "secondary_channels": ["SponsorEvent", "SponsorDrama"],
                "support_channels": ["SearchVolume", "DouyinPush"],
                "primary_ratio": 0.5,
                "secondary_ratio": 0.3,
                "support_ratio": 0.2
            },
            "搜索引擎主导": {
                "description": "以搜索营销为核心，注重意向用户捕获和转化",
                "primary_channels": ["SearchVolume"],
                "secondary_channels": ["Display", "RTBOCPX"],
                "support_channels": ["DouyinPush", "OTTPreroll"],
                "primary_ratio": 0.5,
                "secondary_ratio": 0.3,
                "support_ratio": 0.2
            },
            "全渠道均衡": {
                "description": "各类渠道均衡投放，降低风险，保证全面覆盖",
                "primary_channels": ["SearchVolume", "DouyinKOL", "OTTPreroll", "BuildingLCD"],
                "secondary_channels": ["Display", "RedKOL", "Metro", "DouyinPush"],
                "support_channels": ["OTVPreroll", "WeiboKOL", "RTBOCPX", "DigitalCoop"],
                "primary_ratio": 0.4,
                "secondary_ratio": 0.35,
                "support_ratio": 0.25
            },
            "效果导向": {
                "description": "基于实际测试效果，重点投放高ROI渠道",
                "primary_channels": ["SearchVolume", "DouyinPush"],  # 基于之前测试的高效渠道
                "secondary_channels": ["RTBOCPX"],
                "support_channels": ["OTTPreroll", "Display"],
                "primary_ratio": 0.7,
                "secondary_ratio": 0.2,
                "support_ratio": 0.1
            }
        }
        
    def initialize_excel(self):
        """初始化Excel应用程序并保持打开状态"""
        print("正在初始化Excel...")
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False
            
            print(f"正在打开文件: {self.excel_path}")
            self.workbook = self.excel.Workbooks.Open(self.excel_path)
            self.worksheet = self.workbook.Worksheets("calculation")
            
            # 读取媒体渠道名称
            self.channels = []
            for col in self.input_cols:
                cell_value = self.worksheet.Cells(1, col).Value
                if cell_value:
                    self.channels.append(str(cell_value).strip())
            
            print(f"成功读取到 {len(self.channels)} 个媒体渠道")
            print(f"默认约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}")
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            self.cleanup()
            return False
    
    def apply_constraints(self, budgets):
        """应用渠道约束（默认2%-60%）"""
        constrained_budgets = budgets.copy()
        
        for i, channel in enumerate(self.channels):
            min_budget = self.total_budget * self.default_min_ratio
            max_budget = self.total_budget * self.default_max_ratio
            
            constrained_budgets[i] = max(min_budget, min(max_budget, constrained_budgets[i]))
        
        # 重新归一化到总预算
        total_constrained = sum(constrained_budgets)
        if total_constrained > 0:
            constrained_budgets = [b * self.total_budget / total_constrained for b in constrained_budgets]
        
        return constrained_budgets
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算数据并计算Y_predict总和"""
        try:
            # 应用约束
            constrained_budgets = self.apply_constraints(budgets)
            
            # 清空输入区域
            input_range = self.worksheet.Range("C38:R57")
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.end_row - self.start_row + 1  # 20行
            budgets_per_row = [budget / num_rows for budget in constrained_budgets]
            
            # 写入预算数据到C38:R57
            for row in range(self.start_row, self.end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_cols[i]
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.3)  # 等待计算完成
            
            # 读取U38:U57的Y_predict值并求和
            total_y = 0
            for row in range(self.start_row, self.end_row + 1):
                y_value = self.worksheet.Cells(row, self.output_col).Value
                if y_value is not None:
                    total_y += float(y_value)
            
            return total_y, constrained_budgets
        except Exception as e:
            print(f"计算Y_predict时出错: {e}")
            return 0, budgets
    
    def get_baseline(self):
        """获取基准方案（平均分配）"""
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        baseline_y, constrained_baseline = self.write_budgets_and_calculate(equal_budgets)
        return baseline_y, constrained_baseline
    
    def optimize_strategy(self, strategy_name, strategy_config):
        """优化特定投放策略下的预算分配"""
        print(f"\n=== 优化策略: {strategy_name} ===")
        print(f"策略描述: {strategy_config['description']}")
        
        primary_channels = strategy_config['primary_channels']
        secondary_channels = strategy_config['secondary_channels']
        support_channels = strategy_config['support_channels']
        
        print(f"主要渠道: {primary_channels}")
        print(f"次要渠道: {secondary_channels}")
        print(f"支持渠道: {support_channels}")
        
        best_y = 0
        best_budgets = None
        best_config = None
        
        # 在该策略框架下进行细粒度优化
        # 主要渠道的预算分配比例范围
        primary_ratios = [0.4, 0.5, 0.6, 0.7] if len(primary_channels) <= 2 else [0.4, 0.5, 0.6]
        secondary_ratios = [0.2, 0.25, 0.3, 0.35]
        
        total_tests = len(primary_ratios) * len(secondary_ratios)
        current_test = 0
        
        for primary_ratio in primary_ratios:
            for secondary_ratio in secondary_ratios:
                current_test += 1
                support_ratio = 1.0 - primary_ratio - secondary_ratio
                
                if support_ratio < 0.05:  # 支持渠道至少要有5%预算
                    continue
                
                print(f"  测试配比 {current_test}/{total_tests}: 主要{primary_ratio:.0%}, 次要{secondary_ratio:.0%}, 支持{support_ratio:.0%}")
                
                # 构建预算分配
                budgets = [0.0] * len(self.channels)
                
                # 分配主要渠道预算
                if primary_channels:
                    primary_budget_per_channel = (self.total_budget * primary_ratio) / len(primary_channels)
                    for channel in primary_channels:
                        if channel in self.channels:
                            idx = self.channels.index(channel)
                            budgets[idx] = primary_budget_per_channel
                
                # 分配次要渠道预算
                if secondary_channels:
                    secondary_budget_per_channel = (self.total_budget * secondary_ratio) / len(secondary_channels)
                    for channel in secondary_channels:
                        if channel in self.channels:
                            idx = self.channels.index(channel)
                            budgets[idx] = secondary_budget_per_channel
                
                # 分配支持渠道预算
                if support_channels:
                    support_budget_per_channel = (self.total_budget * support_ratio) / len(support_channels)
                    for channel in support_channels:
                        if channel in self.channels:
                            idx = self.channels.index(channel)
                            budgets[idx] = support_budget_per_channel
                
                # 处理未分配的渠道（给予最小预算）
                unassigned_channels = []
                for i, channel in enumerate(self.channels):
                    if budgets[i] == 0:
                        unassigned_channels.append(i)
                
                if unassigned_channels:
                    min_budget = self.total_budget * self.default_min_ratio  # 使用默认最小比例
                    total_assigned = sum(budgets)
                    remaining_budget = self.total_budget - total_assigned
                    
                    if remaining_budget > 0:
                        budget_per_unassigned = max(min_budget, remaining_budget / len(unassigned_channels))
                        for idx in unassigned_channels:
                            budgets[idx] = budget_per_unassigned
                
                # 重新归一化确保总预算正确
                total_check = sum(budgets)
                if total_check > 0:
                    budgets = [b * self.total_budget / total_check for b in budgets]
                
                # 计算Y_predict
                y_predict, final_budgets = self.write_budgets_and_calculate(budgets)
                
                if y_predict > best_y:
                    best_y = y_predict
                    best_budgets = final_budgets.copy()
                    best_config = {
                        'primary_ratio': primary_ratio,
                        'secondary_ratio': secondary_ratio,
                        'support_ratio': support_ratio
                    }
                    print(f"    ✓ 新的最佳结果: Y_predict = {y_predict:,.2f}")
        
        print(f"策略 '{strategy_name}' 最优结果: Y_predict = {best_y:,.2f}")
        if best_config:
            print(f"最优配比: 主要{best_config['primary_ratio']:.0%}, 次要{best_config['secondary_ratio']:.0%}, 支持{best_config['support_ratio']:.0%}")
        
        return best_y, best_budgets, best_config
    
    def run_strategy_optimization(self):
        """运行所有投放策略的优化"""
        print("=" * 80)
        print("Gatorade 媒体投放策略优化分析（含约束版本）")
        print("=" * 80)

        # 获取基准方案
        baseline_y, baseline_budgets = self.get_baseline()
        print(f"\n基准方案（平均分配+约束）: Y_predict = {baseline_y:,.2f}")

        results = []

        # 添加基准方案
        results.append({
            "方案名称": "基准方案（平均分配+约束）",
            "策略描述": f"所有渠道平均分配预算，应用{self.default_min_ratio:.1%}-{self.default_max_ratio:.1%}约束",
            "Y_predict": baseline_y,
            "预算分配": dict(zip(self.channels, baseline_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })

        # 优化每个投放策略
        for strategy_name, strategy_config in self.strategies.items():
            best_y, best_budgets, best_config = self.optimize_strategy(strategy_name, strategy_config)

            if best_budgets is not None:
                improvement = best_y - baseline_y
                improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0

                results.append({
                    "方案名称": f"{strategy_name}策略",
                    "策略描述": strategy_config['description'],
                    "Y_predict": best_y,
                    "预算分配": dict(zip(self.channels, best_budgets)),
                    "提升": improvement,
                    "提升比例": f"{improvement_pct:.2f}%",
                    "最优配比": best_config
                })

        # 按Y_predict排序
        results.sort(key=lambda x: x["Y_predict"], reverse=True)

        return results, baseline_y

    def export_results_to_new_file(self, results, baseline_y):
        """将结果导出到新的Excel文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"Gatorade媒体投放策略优化结果_含约束_{timestamp}.xlsx"

            print(f"\n正在创建新的分析报告文件: {output_filename}")

            # 创建新的Excel应用实例
            new_excel = win32com.client.Dispatch("Excel.Application")
            new_excel.Visible = False
            new_excel.DisplayAlerts = False

            try:
                # 创建新工作簿
                new_workbook = new_excel.Workbooks.Add()

                # 工作表1: 策略对比总览
                overview_sheet = new_workbook.Worksheets(1)
                overview_sheet.Name = "策略对比总览"

                # 写入标题
                overview_sheet.Cells(1, 1).Value = "Gatorade媒体投放策略优化分析报告（含约束版本）"
                overview_sheet.Cells(1, 1).Font.Size = 16
                overview_sheet.Cells(1, 1).Font.Bold = True
                overview_sheet.Range("A1:H1").Merge()

                overview_sheet.Cells(2, 1).Value = f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                overview_sheet.Cells(3, 1).Value = f"基准方案Y_predict: {baseline_y:,.2f}"
                overview_sheet.Cells(4, 1).Value = f"总预算: {self.total_budget:,}"
                overview_sheet.Cells(5, 1).Value = f"约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}"

                # 策略对比表头
                headers = ["排名", "策略名称", "策略描述", "Y_predict", "提升", "提升比例"]
                for i, header in enumerate(headers, 1):
                    overview_sheet.Cells(7, i).Value = header
                    overview_sheet.Cells(7, i).Font.Bold = True

                # 写入策略对比数据
                for rank, result in enumerate(results, 1):
                    row = rank + 7
                    overview_sheet.Cells(row, 1).Value = rank
                    overview_sheet.Cells(row, 2).Value = result["方案名称"]
                    overview_sheet.Cells(row, 3).Value = result["策略描述"]
                    overview_sheet.Cells(row, 4).Value = result["Y_predict"]
                    overview_sheet.Cells(row, 5).Value = result["提升"]
                    overview_sheet.Cells(row, 6).Value = result["提升比例"]

                # 工作表2: 详细预算分配
                detail_sheet = new_workbook.Worksheets.Add()
                detail_sheet.Name = "详细预算分配"

                # 写入详细预算分配表头
                detail_headers = ["策略名称", "Y_predict", "提升比例"] + self.channels
                for i, header in enumerate(detail_headers, 1):
                    detail_sheet.Cells(1, i).Value = header
                    detail_sheet.Cells(1, i).Font.Bold = True

                # 写入详细预算分配数据
                for rank, result in enumerate(results, 1):
                    row = rank + 1
                    detail_sheet.Cells(row, 1).Value = result["方案名称"]
                    detail_sheet.Cells(row, 2).Value = result["Y_predict"]
                    detail_sheet.Cells(row, 3).Value = result["提升比例"]

                    # 写入各渠道预算配比
                    for i, channel in enumerate(self.channels):
                        budget = result["预算分配"][channel]
                        ratio = budget / self.total_budget
                        detail_sheet.Cells(row, 4 + i).Value = ratio
                        detail_sheet.Cells(row, 4 + i).NumberFormat = "0.00%"

                # 工作表3: 原始输入数据
                raw_data_sheet = new_workbook.Worksheets.Add()
                raw_data_sheet.Name = "原始输入数据"

                # 写入说明
                raw_data_sheet.Cells(1, 1).Value = "原始输入数据（供Excel验证）"
                raw_data_sheet.Cells(1, 1).Font.Bold = True
                raw_data_sheet.Cells(2, 1).Value = "说明：以下数据可直接复制到Excel的C38:R57区域进行验证"
                raw_data_sheet.Cells(3, 1).Value = f"约束条件：每个渠道预算比例在{self.default_min_ratio:.1%}-{self.default_max_ratio:.1%}之间"

                # 写入渠道名称表头
                raw_data_sheet.Cells(5, 1).Value = "策略名称"
                for i, channel in enumerate(self.channels, 2):
                    raw_data_sheet.Cells(5, i).Value = channel

                # 写入原始输入数据
                for rank, result in enumerate(results[:5], 1):  # 只输出前5个方案
                    row = rank + 5
                    raw_data_sheet.Cells(row, 1).Value = result["方案名称"]

                    budgets = [result["预算分配"][ch] for ch in self.channels]
                    budgets_per_row = [budget / 20 for budget in budgets]  # 分配到20行

                    for i, budget_per_row in enumerate(budgets_per_row, 2):
                        raw_data_sheet.Cells(row, i).Value = budget_per_row
                        raw_data_sheet.Cells(row, i).NumberFormat = "0.00"

                # 自动调整所有工作表的列宽
                for sheet in [overview_sheet, detail_sheet, raw_data_sheet]:
                    sheet.Columns.AutoFit()

                # 保存新文件
                output_path = os.path.abspath(output_filename)
                new_workbook.SaveAs(output_path)
                new_workbook.Close()

                print(f"✅ 分析报告已成功导出到: {output_filename}")
                print(f"   文件路径: {output_path}")
                print(f"   包含工作表:")
                print(f"   - 策略对比总览: 各策略效果对比")
                print(f"   - 详细预算分配: 各渠道预算配比")
                print(f"   - 原始输入数据: 供Excel验证的原始数据")

            finally:
                new_excel.Quit()

        except Exception as e:
            print(f"导出新文件时出错: {e}")
            import traceback
            traceback.print_exc()

    def print_strategy_analysis(self, results, baseline_y):
        """打印策略分析报告"""
        print("\n" + "=" * 80)
        print("投放策略分析报告（含约束版本）")
        print("=" * 80)

        print(f"\n基准方案Y_predict: {baseline_y:,.2f}")
        print(f"总预算: {self.total_budget:,}")
        print(f"约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}")

        # 策略效果排名
        print(f"\n投放策略效果排名:")
        print("-" * 100)
        print(f"{'排名':<4} {'策略名称':<25} {'Y_predict':<12} {'提升':<10} {'提升比例':<8} {'策略描述':<30}")
        print("-" * 100)

        for i, result in enumerate(results, 1):
            desc = result["策略描述"][:28] + "..." if len(result["策略描述"]) > 30 else result["策略描述"]
            print(f"{i:<4} {result['方案名称']:<25} {result['Y_predict']:>10,.0f} {result['提升']:>8,.0f} {result['提升比例']:>7} {desc:<30}")

        # 最优策略详细分析
        if len(results) > 1:
            best_result = results[0] if results[0]["方案名称"] != "基准方案（平均分配+约束）" else results[1]
            print(f"\n最优策略详细分析: {best_result['方案名称']}")
            print("-" * 60)
            print(f"策略描述: {best_result['策略描述']}")

            # 按预算从高到低排序
            budget_items = [(channel, budget) for channel, budget in best_result["预算分配"].items()]
            budget_items.sort(key=lambda x: x[1], reverse=True)

            print(f"\n渠道预算分配:")
            for channel, budget in budget_items:
                ratio = budget / self.total_budget
                print(f"  {channel:<15}: {ratio:>6.1%} ({budget:>10,.0f})")

        # 约束效果分析
        print(f"\n约束效果分析:")
        print(f"- 所有渠道预算比例都在 {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%} 范围内")
        print(f"- 避免了极端的预算分配（如某些渠道0投放）")
        print(f"- 确保了投放策略的可执行性和风险控制")

    def cleanup(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass


def main():
    """主函数"""
    excel_file = "Gatorade simulation tool_cal.xlsx"

    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return

    optimizer = GatoradeConstrainedOptimizer(excel_file)

    try:
        # 初始化Excel
        if not optimizer.initialize_excel():
            print("Excel初始化失败，程序退出")
            return

        # 运行策略优化分析
        start_time = datetime.now()
        print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        results, baseline_y = optimizer.run_strategy_optimization()

        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")

        # 导出结果到新的分析报告文件
        optimizer.export_results_to_new_file(results, baseline_y)

        # 打印策略分析报告
        optimizer.print_strategy_analysis(results, baseline_y)

        print(f"\n策略优化完成！请查看生成的Excel分析报告文件。")

    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        optimizer.cleanup()
        print("\n资源清理完成")


if __name__ == "__main__":
    main()
