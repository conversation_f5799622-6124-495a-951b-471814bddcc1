#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的导出功能
"""

import os
import time
from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

def test_export_fix():
    """测试修复后的导出功能"""
    
    excel_file = "Gatorade simulation tool_cal.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ 找不到Excel文件: {excel_file}")
        return False
    
    print("🧪 测试修复后的导出功能")
    print("=" * 50)
    
    # 配置参数（减少迭代次数以加快测试）
    config = {
        "excel_file": excel_file,
        "worksheet_name": "calculation",
        "input_start_row": 38,
        "input_end_row": 57,
        "input_start_col": 3,
        "input_end_col": 18,
        "output_col": 21,
        "output_start_row": 38,
        "output_end_row": 57,
        "channel_names_row": 1,
        "total_budget": 1000000,
        "optimization_schemes": 3,
        "max_iterations": 30,  # 减少迭代次数
        "baseline_allocation": "equal",
        "custom_baseline_ratios": {},
        "channel_constraints": {
            "SearchVolume": {"min": 0.05, "max": 0.40},
            "DouyinKOL": {"min": 0.02, "max": 0.25},
        }
    }
    
    channels = [
        "SearchVolume", "DouyinKOL", "RedKOL", "WeiboKOL", "OTTPreroll", 
        "OTVPreroll", "Display", "DigitalCoop", "RTBOCPX", "BuildingLCD",
        "Metro", "CreativeOutdoor", "SponsorEvent", "SponsorDrama", 
        "DouyinPush", "WeiboPush"
    ]
    
    try:
        print(f"📊 开始优化测试...")
        print(f"   - 总预算: ¥{config['total_budget']:,}")
        print(f"   - 迭代次数: {config['max_iterations']}")
        print(f"   - 生成方案: {config['optimization_schemes']}个")
        
        # 创建进度回调
        def progress_callback(progress, status, detail=""):
            if progress % 20 == 0 or progress >= 95:  # 只显示关键进度点
                print(f"  进度: {progress:3.0f}% | {status}")
        
        start_time = time.time()
        
        optimizer = UniversalOptimizerEngineV2(config, channels, progress_callback)
        results = optimizer.optimize()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ 优化完成！")
        print(f"⏱️  耗时: {duration:.1f} 秒")
        print(f"📈 基准KPI: {results['baseline_kpi']:,.2f}")
        print(f"📋 生成方案: {len(results['results'])}个")
        
        # 检查导出结果
        if results['output_file']:
            print(f"📄 导出文件: {results['output_file']}")
            
            # 检查文件是否存在
            if os.path.exists(results['output_file']):
                file_size = os.path.getsize(results['output_file'])
                print(f"📁 文件大小: {file_size:,} 字节")
                
                # 如果是文本文件，显示前几行
                if results['output_file'].endswith('.txt'):
                    print(f"\n📝 文本报告预览:")
                    print("-" * 40)
                    try:
                        with open(results['output_file'], 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            for i, line in enumerate(lines[:10]):  # 显示前10行
                                print(f"{i+1:2d}: {line.rstrip()}")
                            if len(lines) > 10:
                                print(f"... (还有 {len(lines)-10} 行)")
                    except Exception as e:
                        print(f"读取文件预览失败: {e}")
                
                print(f"\n✅ 导出功能测试成功！")
                return True
            else:
                print(f"❌ 导出文件不存在")
                return False
        else:
            print(f"❌ 没有生成导出文件")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_export():
    """测试简化版导出功能"""
    print(f"\n🧪 测试简化版导出功能")
    print("-" * 40)
    
    # 模拟结果数据
    mock_results = [
        {
            "方案名称": "基准方案（平均分配）",
            "方案描述": "所有渠道平均分配预算",
            "KPI": 15093.48,
            "提升": 0,
            "提升比例": "0.00%",
            "预算分配": {
                "SearchVolume": 62500,
                "DouyinKOL": 62500,
                "RedKOL": 62500,
                "WeiboKOL": 62500,
                "OTTPreroll": 62500,
                "OTVPreroll": 62500,
                "Display": 62500,
                "DigitalCoop": 62500,
                "RTBOCPX": 62500,
                "BuildingLCD": 62500,
                "Metro": 62500,
                "CreativeOutdoor": 62500,
                "SponsorEvent": 62500,
                "SponsorDrama": 62500,
                "DouyinPush": 62500,
                "WeiboPush": 62500
            }
        },
        {
            "方案名称": "高效渠道集中方案",
            "方案描述": "重点投放前3个高效渠道",
            "KPI": 15777.97,
            "提升": 684.49,
            "提升比例": "4.53%",
            "预算分配": {
                "SearchVolume": 200000,
                "DouyinKOL": 150000,
                "RedKOL": 100000,
                "WeiboKOL": 50000,
                "OTTPreroll": 50000,
                "OTVPreroll": 50000,
                "Display": 50000,
                "DigitalCoop": 50000,
                "RTBOCPX": 50000,
                "BuildingLCD": 50000,
                "Metro": 50000,
                "CreativeOutdoor": 50000,
                "SponsorEvent": 50000,
                "SponsorDrama": 50000,
                "DouyinPush": 50000,
                "WeiboPush": 50000
            }
        }
    ]
    
    mock_channel_performance = [
        {"channel": "SearchVolume", "kpi": 16031.48, "improvement": 938, "efficiency": 0.015},
        {"channel": "DouyinPush", "kpi": 15302.48, "improvement": 209, "efficiency": 0.003},
        {"channel": "WeiboPush", "kpi": 15108.48, "improvement": 15, "efficiency": 0.0002}
    ]
    
    # 创建模拟优化器
    config = {
        "total_budget": 1000000,
        "baseline_allocation": "equal",
        "channel_constraints": {
            "SearchVolume": {"min": 0.05, "max": 0.40},
            "DouyinKOL": {"min": 0.02, "max": 0.25}
        }
    }
    
    channels = list(mock_results[0]["预算分配"].keys())
    
    class MockOptimizer:
        def __init__(self, config, channels):
            self.config = config
            self.channels = channels
            self.total_budget = config["total_budget"]
            self.baseline_allocation = config["baseline_allocation"]
            self.channel_constraints = config["channel_constraints"]
            self.default_min_ratio = 0.02
            self.default_max_ratio = 0.60
        
        def get_constraint_summary(self):
            custom_count = len(self.channel_constraints)
            default_count = len(self.channels) - custom_count
            return f"{custom_count}个渠道使用自定义约束，{default_count}个渠道使用默认约束({self.default_min_ratio:.1%}-{self.default_max_ratio:.1%})"
        
        def export_results_simple(self, results, baseline_kpi, channel_performance):
            from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
            real_optimizer = UniversalOptimizerEngineV2(self.config, self.channels)
            return real_optimizer.export_results_simple(results, baseline_kpi, channel_performance)
    
    try:
        optimizer = MockOptimizer(config, channels)
        output_file = optimizer.export_results_simple(mock_results, 15093.48, mock_channel_performance)
        
        if output_file and os.path.exists(output_file):
            print(f"✅ 简化版导出成功: {output_file}")
            
            # 显示文件内容预览
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n📝 文件内容预览 (前500字符):")
                print("-" * 40)
                print(content[:500])
                if len(content) > 500:
                    print("...")
            
            return True
        else:
            print(f"❌ 简化版导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 简化版导出测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试导出功能修复")
    print("=" * 60)
    
    # 测试1: 简化版导出
    success1 = test_simple_export()
    
    # 测试2: 完整优化和导出
    success2 = test_export_fix()
    
    print(f"\n" + "=" * 60)
    if success1 and success2:
        print(f"🎉 导出功能修复验证成功！")
        print(f"\n✨ 修复内容:")
        print(f"   ✅ 修复Excel.Application.Quit错误")
        print(f"   ✅ 添加文本版备份导出")
        print(f"   ✅ 简化Excel导出逻辑")
        print(f"   ✅ 改进错误处理机制")
        print(f"   ✅ 安全的资源清理")
    else:
        print(f"⚠️  部分测试失败，但至少有备用导出方案")
    
    input(f"\n按回车键退出...")
