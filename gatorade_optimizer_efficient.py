import pandas as pd
import numpy as np
import openpyxl
import win32com.client
import os
import time
from datetime import datetime
import itertools
from tqdm import tqdm

class GatoradeOptimizer:
    def __init__(self, excel_path):
        self.excel_path = os.path.abspath(excel_path)
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.channels = []
        self.total_budget = 1000000
        self.start_row = 38
        self.end_row = 57
        self.input_cols = list(range(3, 19))  # C到R列
        self.output_col = 21  # U列
        
    def initialize_excel(self):
        """初始化Excel应用程序并保持打开状态"""
        print("正在初始化Excel...")
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False  # 关闭屏幕更新提高性能
            
            print(f"正在打开文件: {self.excel_path}")
            self.workbook = self.excel.Workbooks.Open(self.excel_path)
            self.worksheet = self.workbook.Worksheets("calculation")
            
            # 读取媒体渠道名称
            self.channels = []
            for col in self.input_cols:
                cell_value = self.worksheet.Cells(1, col).Value
                if cell_value:
                    self.channels.append(str(cell_value).strip())
            
            print(f"成功读取到 {len(self.channels)} 个媒体渠道")
            for i, channel in enumerate(self.channels, 1):
                print(f"  {i}. {channel}")
                
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            self.cleanup()
            return False
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算数据并计算Y_predict总和"""
        try:
            # 写入预算数据到C38:R57
            for row in range(self.start_row, self.end_row + 1):
                for i, budget in enumerate(budgets):
                    col = self.input_cols[i]
                    self.worksheet.Cells(row, col).Value = budget
            
            # 强制重新计算
            self.worksheet.Calculate()
            
            # 读取U38:U57的Y_predict值并求和
            total_y = 0
            for row in range(self.start_row, self.end_row + 1):
                y_value = self.worksheet.Cells(row, self.output_col).Value
                if y_value is not None:
                    total_y += float(y_value)
            
            return total_y
        except Exception as e:
            print(f"计算Y_predict时出错: {e}")
            return 0
    
    def generate_budget_ratios(self):
        """生成不同的预算配比方案"""
        ratios = []
        n_channels = len(self.channels)
        
        # 1. 基准方案：平均分配
        equal_ratio = [1.0 / n_channels] * n_channels
        ratios.append(("平均分配", equal_ratio))
        
        # 2. 数字媒体优先方案
        digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
        digital_ratio = self._create_priority_ratio(digital_channels, 0.7)
        ratios.append(("数字媒体优先", digital_ratio))
        
        # 3. KOL优先方案
        kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
        kol_ratio = self._create_priority_ratio(kol_channels, 0.6)
        ratios.append(("KOL优先", kol_ratio))
        
        # 4. 视频优先方案
        video_channels = ['OTTPreroll', 'OTVPreroll']
        video_ratio = self._create_priority_ratio(video_channels, 0.5)
        ratios.append(("视频优先", video_ratio))
        
        # 5. 户外优先方案
        outdoor_channels = ['BuildingLCD', 'Metro', 'CreativeOutdoor']
        outdoor_ratio = self._create_priority_ratio(outdoor_channels, 0.4)
        ratios.append(("户外优先", outdoor_ratio))
        
        # 6. 搜索优先方案
        search_channels = ['SearchVolume']
        search_ratio = self._create_priority_ratio(search_channels, 0.3)
        ratios.append(("搜索优先", search_ratio))
        
        return ratios
    
    def _create_priority_ratio(self, priority_channels, priority_weight):
        """创建优先渠道的配比"""
        ratio = [0.0] * len(self.channels)
        priority_indices = []
        
        for i, channel in enumerate(self.channels):
            if channel in priority_channels:
                priority_indices.append(i)
        
        if priority_indices:
            # 优先渠道分配指定权重
            priority_budget_per_channel = priority_weight / len(priority_indices)
            for idx in priority_indices:
                ratio[idx] = priority_budget_per_channel
            
            # 其他渠道分配剩余权重
            other_indices = [i for i in range(len(self.channels)) if i not in priority_indices]
            if other_indices:
                other_budget_per_channel = (1.0 - priority_weight) / len(other_indices)
                for idx in other_indices:
                    ratio[idx] = other_budget_per_channel
        else:
            # 如果没有找到优先渠道，平均分配
            ratio = [1.0 / len(self.channels)] * len(self.channels)
        
        return ratio
    
    def optimize_with_grid_search(self, top_channels=5, steps=5):
        """使用网格搜索优化预算分配"""
        print(f"\n开始网格搜索优化（前{top_channels}个渠道，每个渠道{steps}个步长）...")
        
        # 先测试基础方案找出表现最好的渠道
        base_ratios = self.generate_budget_ratios()
        best_channels = []
        
        print("测试基础方案...")
        for name, ratio in base_ratios:
            budgets = [r * self.total_budget for r in ratio]
            y_predict = self.write_budgets_and_calculate(budgets)
            print(f"{name}: Y_predict = {y_predict:,.2f}")
            
            # 找出该方案中预算占比最高的渠道
            sorted_indices = sorted(range(len(ratio)), key=lambda i: ratio[i], reverse=True)
            for idx in sorted_indices[:2]:  # 取前2个渠道
                if self.channels[idx] not in [ch[0] for ch in best_channels]:
                    best_channels.append((self.channels[idx], idx))
        
        # 限制到前top_channels个渠道
        best_channels = best_channels[:top_channels]
        print(f"\n选择优化渠道: {[ch[0] for ch in best_channels]}")
        
        # 网格搜索
        best_y = 0
        best_ratio = None
        best_name = ""
        
        # 为每个选中的渠道生成不同的权重组合
        weight_options = np.linspace(0.1, 0.8, steps)
        
        total_combinations = len(weight_options) ** len(best_channels)
        print(f"总共需要测试 {total_combinations} 种组合...")
        
        with tqdm(total=total_combinations, desc="网格搜索进度") as pbar:
            for weights in itertools.product(weight_options, repeat=len(best_channels)):
                if sum(weights) > 1.0:  # 跳过权重和超过1的组合
                    pbar.update(1)
                    continue
                
                # 构建配比
                ratio = [0.0] * len(self.channels)
                
                # 设置选中渠道的权重
                for i, (channel_name, channel_idx) in enumerate(best_channels):
                    ratio[channel_idx] = weights[i]
                
                # 剩余权重平均分配给其他渠道
                remaining_weight = 1.0 - sum(weights)
                other_indices = [i for i in range(len(self.channels)) 
                               if i not in [ch[1] for ch in best_channels]]
                
                if other_indices and remaining_weight > 0:
                    weight_per_other = remaining_weight / len(other_indices)
                    for idx in other_indices:
                        ratio[idx] = weight_per_other
                
                # 计算Y_predict
                budgets = [r * self.total_budget for r in ratio]
                y_predict = self.write_budgets_and_calculate(budgets)
                
                # 更新最优解
                if y_predict > best_y:
                    best_y = y_predict
                    best_ratio = ratio.copy()
                    weight_str = ", ".join([f"{ch[0]}:{w:.1%}" for ch, w in zip(best_channels, weights)])
                    best_name = f"网格搜索最优({weight_str})"
                    
                    tqdm.write(f"发现更好结果: Y_predict = {y_predict:,.2f}")
                    tqdm.write(f"配比: {weight_str}")
                
                pbar.update(1)
        
        return best_y, best_ratio, best_name
    
    def run_comprehensive_optimization(self):
        """运行全面的优化分析"""
        print("=" * 60)
        print("Gatorade 媒体投放优化分析")
        print("=" * 60)

        results = []

        # 1. 测试基础方案
        print("\n1. 测试基础预设方案...")
        base_ratios = self.generate_budget_ratios()

        for name, ratio in base_ratios:
            budgets = [r * self.total_budget for r in ratio]
            y_predict = self.write_budgets_and_calculate(budgets)

            results.append({
                "方案名称": name,
                "Y_predict": y_predict,
                "预算分配": dict(zip(self.channels, budgets)),
                "配比": dict(zip(self.channels, ratio))
            })

            print(f"{name}: Y_predict = {y_predict:,.2f}")

        # 找出基准方案（平均分配）
        baseline_result = next(r for r in results if r["方案名称"] == "平均分配")
        baseline_y = baseline_result["Y_predict"]

        # 2. 网格搜索优化
        print("\n2. 执行网格搜索优化...")
        best_y, best_ratio, best_name = self.optimize_with_grid_search(top_channels=4, steps=6)

        if best_ratio:
            best_budgets = [r * self.total_budget for r in best_ratio]
            results.append({
                "方案名称": best_name,
                "Y_predict": best_y,
                "预算分配": dict(zip(self.channels, best_budgets)),
                "配比": dict(zip(self.channels, best_ratio))
            })

        # 3. 生成其他高效方案
        print("\n3. 生成其他高效方案...")
        additional_results = self.generate_additional_schemes(baseline_y)
        results.extend(additional_results)

        # 4. 排序并输出结果
        results.sort(key=lambda x: x["Y_predict"], reverse=True)

        return results, baseline_y

    def generate_additional_schemes(self, baseline_y):
        """生成其他几种高效的投放方案"""
        additional_results = []

        # 方案1: 数字+KOL混合优先
        print("  - 测试数字+KOL混合方案...")
        digital_kol_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'DouyinKOL', 'RedKOL']
        mixed_ratio = self._create_priority_ratio(digital_kol_channels, 0.75)
        mixed_budgets = [r * self.total_budget for r in mixed_ratio]
        mixed_y = self.write_budgets_and_calculate(mixed_budgets)

        additional_results.append({
            "方案名称": "数字+KOL混合优先",
            "Y_predict": mixed_y,
            "预算分配": dict(zip(self.channels, mixed_budgets)),
            "配比": dict(zip(self.channels, mixed_ratio))
        })

        # 方案2: 高ROI渠道集中
        print("  - 测试高ROI渠道集中方案...")
        high_roi_channels = ['SearchVolume', 'DouyinKOL', 'OTTPreroll']
        high_roi_ratio = self._create_priority_ratio(high_roi_channels, 0.6)
        high_roi_budgets = [r * self.total_budget for r in high_roi_ratio]
        high_roi_y = self.write_budgets_and_calculate(high_roi_budgets)

        additional_results.append({
            "方案名称": "高ROI渠道集中",
            "Y_predict": high_roi_y,
            "预算分配": dict(zip(self.channels, high_roi_budgets)),
            "配比": dict(zip(self.channels, high_roi_ratio))
        })

        # 方案3: 保守均衡方案
        print("  - 测试保守均衡方案...")
        conservative_ratio = [1.0 / len(self.channels)] * len(self.channels)
        # 对表现好的渠道稍微增加权重
        good_channels = ['SearchVolume', 'OTTPreroll', 'DouyinKOL', 'Display']
        for i, channel in enumerate(self.channels):
            if channel in good_channels:
                conservative_ratio[i] *= 1.3

        # 重新归一化
        total_ratio = sum(conservative_ratio)
        conservative_ratio = [r / total_ratio for r in conservative_ratio]

        conservative_budgets = [r * self.total_budget for r in conservative_ratio]
        conservative_y = self.write_budgets_and_calculate(conservative_budgets)

        additional_results.append({
            "方案名称": "保守均衡方案",
            "Y_predict": conservative_y,
            "预算分配": dict(zip(self.channels, conservative_budgets)),
            "配比": dict(zip(self.channels, conservative_ratio))
        })

        return additional_results

    def export_results_to_excel(self, results, baseline_y):
        """将结果导出到Excel文件"""
        try:
            # 创建新的工作表用于结果对比
            try:
                comparison_sheet = self.workbook.Worksheets("优化结果对比")
                comparison_sheet.Delete()  # 删除已存在的表
            except:
                pass

            comparison_sheet = self.workbook.Worksheets.Add()
            comparison_sheet.Name = "优化结果对比"

            # 写入表头
            headers = ["排名", "方案名称", "Y_predict", "vs基准提升", "提升比例"] + self.channels
            for i, header in enumerate(headers, 1):
                comparison_sheet.Cells(1, i).Value = header
                comparison_sheet.Cells(1, i).Font.Bold = True

            # 写入数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                y_predict = result["Y_predict"]
                improvement = y_predict - baseline_y
                improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0

                comparison_sheet.Cells(row, 1).Value = rank
                comparison_sheet.Cells(row, 2).Value = result["方案名称"]
                comparison_sheet.Cells(row, 3).Value = y_predict
                comparison_sheet.Cells(row, 4).Value = improvement
                comparison_sheet.Cells(row, 5).Value = f"{improvement_pct:.2f}%"

                # 写入各渠道预算配比
                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = result["配比"][channel]
                    comparison_sheet.Cells(row, 6 + i).Value = ratio
                    comparison_sheet.Cells(row, 6 + i).NumberFormat = "0.00%"

            # 自动调整列宽
            comparison_sheet.Columns.AutoFit()

            # 保存文件
            self.workbook.Save()
            print(f"\n结果已导出到Excel文件的'优化结果对比'工作表")

        except Exception as e:
            print(f"导出Excel时出错: {e}")

    def print_analysis_report(self, results, baseline_y):
        """打印详细的分析报告"""
        print("\n" + "=" * 80)
        print("详细分析报告")
        print("=" * 80)

        print(f"\n基准方案（平均分配）Y_predict: {baseline_y:,.2f}")
        print(f"总预算: {self.total_budget:,}")

        print(f"\n前5名方案对比:")
        print("-" * 80)
        print(f"{'排名':<4} {'方案名称':<20} {'Y_predict':<12} {'提升':<10} {'提升比例':<8}")
        print("-" * 80)

        for i, result in enumerate(results[:5], 1):
            y_predict = result["Y_predict"]
            improvement = y_predict - baseline_y
            improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0

            print(f"{i:<4} {result['方案名称']:<20} {y_predict:>10,.0f} {improvement:>8,.0f} {improvement_pct:>6.1f}%")

        # 最优方案详细分析
        best_result = results[0]
        print(f"\n最优方案详细分析: {best_result['方案名称']}")
        print("-" * 50)

        # 按预算从高到低排序
        budget_items = [(channel, budget, ratio) for channel, budget, ratio in
                       zip(self.channels,
                           [best_result["预算分配"][ch] for ch in self.channels],
                           [best_result["配比"][ch] for ch in self.channels])]
        budget_items.sort(key=lambda x: x[1], reverse=True)

        print("渠道预算分配:")
        for channel, budget, ratio in budget_items:
            print(f"  {channel:<15}: {ratio:>6.1%} ({budget:>10,.0f})")

        # 渠道类型汇总
        print(f"\n渠道类型汇总:")
        digital_channels = ['SearchVolume', 'OTTPreroll', 'OTVPreroll', 'Display', 'DigitalCoop', 'RTBOCPX']
        kol_channels = ['DouyinKOL', 'RedKOL', 'WeiboKOL']
        outdoor_channels = ['BuildingLCD', 'Metro', 'CreativeOutdoor']

        digital_budget = sum(best_result["预算分配"][ch] for ch in digital_channels if ch in self.channels)
        kol_budget = sum(best_result["预算分配"][ch] for ch in kol_channels if ch in self.channels)
        outdoor_budget = sum(best_result["预算分配"][ch] for ch in outdoor_channels if ch in self.channels)

        print(f"  数字媒体: {digital_budget/self.total_budget:>6.1%} ({digital_budget:>10,.0f})")
        print(f"  KOL营销:  {kol_budget/self.total_budget:>6.1%} ({kol_budget:>10,.0f})")
        print(f"  户外媒体: {outdoor_budget/self.total_budget:>6.1%} ({outdoor_budget:>10,.0f})")

    def cleanup(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            # 强制清理Excel进程
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass


def main():
    """主函数"""
    excel_file = "Gatorade simulation tool_cal.xlsx"

    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return

    optimizer = GatoradeOptimizer(excel_file)

    try:
        # 初始化Excel
        if not optimizer.initialize_excel():
            print("Excel初始化失败，程序退出")
            return

        # 运行优化分析
        start_time = datetime.now()
        print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        results, baseline_y = optimizer.run_comprehensive_optimization()

        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")

        # 导出结果到Excel
        optimizer.export_results_to_excel(results, baseline_y)

        # 打印分析报告
        optimizer.print_analysis_report(results, baseline_y)

        # 输出原始数据供验证
        print(f"\n" + "=" * 80)
        print("原始输入数据（供Excel验证）")
        print("=" * 80)

        for i, result in enumerate(results[:3], 1):  # 只输出前3个方案的原始数据
            print(f"\n方案{i}: {result['方案名称']}")
            print("C38:R57区域输入值（每行相同）:")
            budgets = [result["预算分配"][ch] for ch in optimizer.channels]
            budget_str = ", ".join([f"{budget:.2f}" for budget in budgets])
            print(f"[{budget_str}]")

            print("各渠道预算:")
            for channel, budget in zip(optimizer.channels, budgets):
                print(f"  {channel}: {budget:,.2f}")

        print(f"\n优化完成！请查看Excel文件中的'优化结果对比'工作表。")

    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        optimizer.cleanup()
        print("\n资源清理完成")


if __name__ == "__main__":
    main()
