import win32com.client
import os
import time
import random
import numpy as np
from datetime import datetime

class UniversalOptimizerEngine:
    def __init__(self, config, channels):
        self.config = config
        self.channels = channels
        self.excel = None
        self.workbook = None
        self.worksheet = None
        
        # 从配置中获取参数
        self.excel_file = config["excel_file"]
        self.worksheet_name = config["worksheet_name"]
        self.input_start_row = config["input_start_row"]
        self.input_end_row = config["input_end_row"]
        self.input_start_col = config["input_start_col"]
        self.input_end_col = config["input_end_col"]
        self.output_col = config["output_col"]
        self.total_budget = config["total_budget"]
        self.optimization_schemes = config["optimization_schemes"]
        self.baseline_allocation = config["baseline_allocation"]
        self.channel_constraints = config.get("channel_constraints", {})
        
    def initialize_excel(self):
        """初始化Excel连接"""
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False
            
            self.workbook = self.excel.Workbooks.Open(os.path.abspath(self.excel_file))
            self.worksheet = self.workbook.Worksheets(self.worksheet_name)
            
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            return False
    
    def cleanup_excel(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算并计算KPI"""
        try:
            # 清空输入区域
            input_range = self.worksheet.Range(
                self.worksheet.Cells(self.input_start_row, self.input_start_col),
                self.worksheet.Cells(self.input_end_row, self.input_end_col)
            )
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.input_end_row - self.input_start_row + 1
            budgets_per_row = [budget / num_rows for budget in budgets]
            
            # 写入预算数据
            for row in range(self.input_start_row, self.input_end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_start_col + i
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.3)
            
            # 读取输出值并求和
            total_kpi = 0
            for row in range(self.input_start_row, self.input_end_row + 1):
                kpi_value = self.worksheet.Cells(row, self.output_col).Value
                if kpi_value is not None:
                    total_kpi += float(kpi_value)
            
            return total_kpi
        except Exception as e:
            print(f"计算KPI时出错: {e}")
            return 0
    
    def get_baseline_kpi(self):
        """获取基准方案的KPI"""
        if self.baseline_allocation == "equal":
            # 平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        else:
            # 自定义基准方案（这里可以扩展）
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        baseline_kpi = self.write_budgets_and_calculate(equal_budgets)
        return baseline_kpi, equal_budgets
    
    def apply_constraints(self, budgets):
        """应用渠道约束"""
        constrained_budgets = budgets.copy()
        
        for i, channel in enumerate(self.channels):
            if channel in self.channel_constraints:
                constraint = self.channel_constraints[channel]
                min_budget = self.total_budget * constraint["min"]
                max_budget = self.total_budget * constraint["max"]
                
                constrained_budgets[i] = max(min_budget, min(max_budget, constrained_budgets[i]))
        
        # 重新归一化到总预算
        total_constrained = sum(constrained_budgets)
        if total_constrained > 0:
            constrained_budgets = [b * self.total_budget / total_constrained for b in constrained_budgets]
        
        return constrained_budgets
    
    def test_individual_channels(self, baseline_kpi):
        """测试各渠道单独表现"""
        channel_performance = []
        
        for i, channel in enumerate(self.channels):
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            # 应用约束
            test_budgets = self.apply_constraints(test_budgets)
            
            kpi = self.write_budgets_and_calculate(test_budgets)
            improvement = kpi - baseline_kpi
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'kpi': kpi,
                'improvement': improvement,
                'efficiency': improvement / (self.total_budget * 0.3)
            })
        
        # 按改进效果排序
        channel_performance.sort(key=lambda x: x['improvement'], reverse=True)
        return channel_performance
    
    def generate_optimization_schemes(self, baseline_kpi, channel_performance):
        """生成优化方案"""
        results = []
        
        # 添加基准方案
        baseline_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        results.append({
            "方案名称": "基准方案（平均分配）",
            "KPI": baseline_kpi,
            "预算分配": dict(zip(self.channels, baseline_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 获取高效渠道
        top_channels = [perf['channel'] for perf in channel_performance[:6] if perf['improvement'] > 0]
        
        if len(top_channels) >= 2:
            # 方案1: 高效渠道集中
            budgets = [0.0] * len(self.channels)
            high_budget = self.total_budget * 0.6 / len(top_channels[:3])
            low_budget = self.total_budget * 0.4 / (len(self.channels) - 3)
            
            for i, channel in enumerate(self.channels):
                if channel in top_channels[:3]:
                    budgets[i] = high_budget
                else:
                    budgets[i] = low_budget
            
            budgets = self.apply_constraints(budgets)
            kpi = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi
            improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0
            
            results.append({
                "方案名称": "高效渠道集中方案",
                "KPI": kpi,
                "预算分配": dict(zip(self.channels, budgets)),
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
        
        # 方案2: 渐进式优化
        budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        for i, channel in enumerate(self.channels):
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf:
                rank = channel_performance.index(perf) + 1
                if rank <= 3:
                    budgets[i] *= 1.5
                elif rank <= 6:
                    budgets[i] *= 1.2
                elif rank > 10:
                    budgets[i] *= 0.7
        
        # 重新归一化
        total_check = sum(budgets)
        budgets = [b * self.total_budget / total_check for b in budgets]
        budgets = self.apply_constraints(budgets)
        
        kpi = self.write_budgets_and_calculate(budgets)
        improvement = kpi - baseline_kpi
        improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0
        
        results.append({
            "方案名称": "渐进式优化方案",
            "KPI": kpi,
            "预算分配": dict(zip(self.channels, budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        
        # 方案3-N: 随机搜索优化
        best_random_results = []
        
        for iteration in range(min(100, self.optimization_schemes * 20)):
            # 生成随机权重
            weights = [random.uniform(0.1, 3.0) for _ in self.channels]
            
            # 偏向高效渠道
            for i, channel in enumerate(self.channels):
                perf = next((p for p in channel_performance if p['channel'] == channel), None)
                if perf and perf['improvement'] > 0:
                    rank = channel_performance.index(perf) + 1
                    if rank <= 3:
                        weights[i] *= 2.0
                    elif rank <= 6:
                        weights[i] *= 1.5
            
            # 归一化权重
            total_weight = sum(weights)
            ratios = [w / total_weight for w in weights]
            budgets = [r * self.total_budget for r in ratios]
            budgets = self.apply_constraints(budgets)
            
            kpi = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi
            
            if improvement > 0:
                improvement_pct = (improvement / baseline_kpi) * 100
                best_random_results.append({
                    "方案名称": f"智能搜索方案{len(best_random_results)+1}",
                    "KPI": kpi,
                    "预算分配": dict(zip(self.channels, budgets)),
                    "提升": improvement,
                    "提升比例": f"{improvement_pct:.2f}%"
                })
        
        # 按KPI排序，取前几个
        best_random_results.sort(key=lambda x: x["KPI"], reverse=True)
        results.extend(best_random_results[:self.optimization_schemes-2])
        
        # 按KPI排序所有结果
        results.sort(key=lambda x: x["KPI"], reverse=True)
        
        return results
    
    def export_results(self, results, baseline_kpi):
        """导出结果到新文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"媒体预算优化结果_{timestamp}.xlsx"
            
            # 创建新的Excel文件
            new_excel = win32com.client.Dispatch("Excel.Application")
            new_excel.Visible = False
            new_excel.DisplayAlerts = False
            
            try:
                new_workbook = new_excel.Workbooks.Add()
                
                # 结果总览工作表
                overview_sheet = new_workbook.Worksheets(1)
                overview_sheet.Name = "优化结果总览"
                
                # 写入标题和基本信息
                overview_sheet.Cells(1, 1).Value = "媒体预算优化分析报告"
                overview_sheet.Cells(1, 1).Font.Size = 16
                overview_sheet.Cells(1, 1).Font.Bold = True
                
                overview_sheet.Cells(3, 1).Value = f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                overview_sheet.Cells(4, 1).Value = f"基准方案KPI: {baseline_kpi:,.2f}"
                overview_sheet.Cells(5, 1).Value = f"总预算: {self.total_budget:,}"
                
                # 结果表头
                headers = ["排名", "方案名称", "KPI", "提升", "提升比例"]
                for i, header in enumerate(headers, 1):
                    overview_sheet.Cells(7, i).Value = header
                    overview_sheet.Cells(7, i).Font.Bold = True
                
                # 写入结果数据
                for rank, result in enumerate(results, 1):
                    row = rank + 7
                    overview_sheet.Cells(row, 1).Value = rank
                    overview_sheet.Cells(row, 2).Value = result["方案名称"]
                    overview_sheet.Cells(row, 3).Value = result["KPI"]
                    overview_sheet.Cells(row, 4).Value = result["提升"]
                    overview_sheet.Cells(row, 5).Value = result["提升比例"]
                
                # 详细预算分配工作表
                detail_sheet = new_workbook.Worksheets.Add()
                detail_sheet.Name = "详细预算分配"
                
                detail_headers = ["方案名称", "KPI"] + self.channels
                for i, header in enumerate(detail_headers, 1):
                    detail_sheet.Cells(1, i).Value = header
                    detail_sheet.Cells(1, i).Font.Bold = True
                
                for rank, result in enumerate(results, 1):
                    row = rank + 1
                    detail_sheet.Cells(row, 1).Value = result["方案名称"]
                    detail_sheet.Cells(row, 2).Value = result["KPI"]
                    
                    for i, channel in enumerate(self.channels):
                        budget = result["预算分配"][channel]
                        ratio = budget / self.total_budget
                        detail_sheet.Cells(row, 3 + i).Value = ratio
                        detail_sheet.Cells(row, 3 + i).NumberFormat = "0.00%"
                
                # 自动调整列宽
                overview_sheet.Columns.AutoFit()
                detail_sheet.Columns.AutoFit()
                
                # 保存文件
                output_path = os.path.abspath(output_filename)
                new_workbook.SaveAs(output_path)
                new_workbook.Close()
                
                return output_filename
                
            finally:
                new_excel.Quit()
                
        except Exception as e:
            print(f"导出结果时出错: {e}")
            return None
    
    def optimize(self):
        """执行优化"""
        if not self.initialize_excel():
            raise Exception("无法初始化Excel连接")
        
        try:
            # 获取基准KPI
            baseline_kpi, baseline_budgets = self.get_baseline_kpi()
            
            # 测试各渠道表现
            channel_performance = self.test_individual_channels(baseline_kpi)
            
            # 生成优化方案
            results = self.generate_optimization_schemes(baseline_kpi, channel_performance)
            
            # 导出结果
            output_file = self.export_results(results, baseline_kpi)
            
            return {
                "results": results,
                "baseline_kpi": baseline_kpi,
                "channel_performance": channel_performance,
                "output_file": output_file
            }
            
        finally:
            self.cleanup_excel()
