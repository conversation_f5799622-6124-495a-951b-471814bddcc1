import pandas as pd
import numpy as np
import openpyxl
import win32com.client
import os
import time
from datetime import datetime

class GatoradeFastOptimizer:
    def __init__(self, excel_path):
        self.excel_path = os.path.abspath(excel_path)
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.channels = []
        self.total_budget = 1000000
        self.start_row = 38
        self.end_row = 57
        self.input_cols = list(range(3, 19))  # C到R列
        self.output_col = 21  # U列
        
    def initialize_excel(self):
        """初始化Excel应用程序并保持打开状态"""
        print("正在初始化Excel...")
        try:
            self.excel = win32com.client.Dispatch("Excel.Application")
            self.excel.Visible = False
            self.excel.DisplayAlerts = False
            self.excel.ScreenUpdating = False
            
            print(f"正在打开文件: {self.excel_path}")
            self.workbook = self.excel.Workbooks.Open(self.excel_path)
            self.worksheet = self.workbook.Worksheets("calculation")
            
            # 读取媒体渠道名称
            self.channels = []
            for col in self.input_cols:
                cell_value = self.worksheet.Cells(1, col).Value
                if cell_value:
                    self.channels.append(str(cell_value).strip())
            
            print(f"成功读取到 {len(self.channels)} 个媒体渠道")
            return True
        except Exception as e:
            print(f"初始化Excel失败: {e}")
            self.cleanup()
            return False
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算数据并计算Y_predict总和"""
        try:
            # 写入预算数据到C38:R57
            for row in range(self.start_row, self.end_row + 1):
                for i, budget in enumerate(budgets):
                    col = self.input_cols[i]
                    self.worksheet.Cells(row, col).Value = budget
            
            # 强制重新计算
            self.worksheet.Calculate()
            
            # 读取U38:U57的Y_predict值并求和
            total_y = 0
            for row in range(self.start_row, self.end_row + 1):
                y_value = self.worksheet.Cells(row, self.output_col).Value
                if y_value is not None:
                    total_y += float(y_value)
            
            return total_y
        except Exception as e:
            print(f"计算Y_predict时出错: {e}")
            return 0
    
    def run_fast_optimization(self):
        """运行快速优化分析"""
        print("=" * 60)
        print("Gatorade 媒体投放快速优化分析")
        print("=" * 60)
        
        results = []
        
        # 1. 基准方案：平均分配
        print("\n1. 计算基准方案（平均分配）...")
        equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        baseline_y = self.write_budgets_and_calculate(equal_budgets)
        
        results.append({
            "方案名称": "基准方案（平均分配）",
            "Y_predict": baseline_y,
            "预算分配": dict(zip(self.channels, equal_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })
        print(f"基准方案 Y_predict: {baseline_y:,.2f}")
        
        # 2. 基于观察到的趋势生成优化方案
        optimization_schemes = [
            # KOL重点方案（基于之前观察DouyinKOL表现很好）
            {
                "name": "KOL重点方案",
                "DouyinKOL": 0.40,
                "RedKOL": 0.20,
                "WeiboKOL": 0.10,
                "SearchVolume": 0.10,
                "OTTPreroll": 0.10,
                "others": 0.10
            },
            # 数字+KOL混合方案
            {
                "name": "数字+KOL混合方案",
                "DouyinKOL": 0.25,
                "RedKOL": 0.15,
                "SearchVolume": 0.15,
                "OTTPreroll": 0.15,
                "Display": 0.10,
                "others": 0.20
            },
            # 视频+KOL方案
            {
                "name": "视频+KOL方案",
                "DouyinKOL": 0.30,
                "OTTPreroll": 0.20,
                "OTVPreroll": 0.15,
                "RedKOL": 0.15,
                "SearchVolume": 0.10,
                "others": 0.10
            },
            # 搜索+KOL方案
            {
                "name": "搜索+KOL方案",
                "SearchVolume": 0.25,
                "DouyinKOL": 0.25,
                "RedKOL": 0.15,
                "Display": 0.15,
                "OTTPreroll": 0.10,
                "others": 0.10
            },
            # 极致KOL方案
            {
                "name": "极致KOL方案",
                "DouyinKOL": 0.50,
                "RedKOL": 0.25,
                "WeiboKOL": 0.10,
                "SearchVolume": 0.08,
                "OTTPreroll": 0.05,
                "others": 0.02
            },
            # 保守优化方案
            {
                "name": "保守优化方案",
                "DouyinKOL": 0.20,
                "RedKOL": 0.15,
                "SearchVolume": 0.15,
                "OTTPreroll": 0.12,
                "Display": 0.10,
                "others": 0.28
            }
        ]
        
        print(f"\n2. 测试 {len(optimization_schemes)} 个优化方案...")
        
        for scheme in optimization_schemes:
            print(f"\n测试方案: {scheme['name']}")
            
            # 构建预算分配
            budgets = [0.0] * len(self.channels)
            
            # 设置指定渠道的预算
            total_specified = 0
            for channel, ratio in scheme.items():
                if channel == "name":
                    continue
                if channel == "others":
                    continue
                if channel in self.channels:
                    idx = self.channels.index(channel)
                    budgets[idx] = ratio * self.total_budget
                    total_specified += ratio
            
            # 分配剩余预算给其他渠道
            others_ratio = scheme.get("others", 0)
            remaining_budget = others_ratio * self.total_budget
            other_channels = [i for i, ch in enumerate(self.channels) 
                            if budgets[i] == 0]
            
            if other_channels and remaining_budget > 0:
                budget_per_other = remaining_budget / len(other_channels)
                for idx in other_channels:
                    budgets[idx] = budget_per_other
            
            # 验证总预算
            total_check = sum(budgets)
            if abs(total_check - self.total_budget) > 1:
                print(f"警告: 总预算不匹配 {total_check:,.2f} vs {self.total_budget:,.2f}")
                continue
            
            # 计算Y_predict
            y_predict = self.write_budgets_and_calculate(budgets)
            improvement = y_predict - baseline_y
            improvement_pct = (improvement / baseline_y) * 100 if baseline_y > 0 else 0
            
            results.append({
                "方案名称": scheme['name'],
                "Y_predict": y_predict,
                "预算分配": dict(zip(self.channels, budgets)),
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
            
            print(f"  Y_predict: {y_predict:,.2f}")
            print(f"  提升: {improvement:,.2f} ({improvement_pct:.2f}%)")
            
            # 显示主要渠道配比
            main_channels = [(ch, budget) for ch, budget in zip(self.channels, budgets) if budget > self.total_budget * 0.05]
            main_channels.sort(key=lambda x: x[1], reverse=True)
            print("  主要渠道配比:")
            for ch, budget in main_channels:
                ratio = budget / self.total_budget
                print(f"    {ch}: {ratio:.1%} ({budget:,.0f})")
        
        # 排序结果
        results.sort(key=lambda x: x["Y_predict"], reverse=True)
        
        return results, baseline_y
    
    def export_and_display_results(self, results, baseline_y):
        """导出结果并显示分析报告"""
        # 导出到Excel
        try:
            comparison_sheet = self.workbook.Worksheets.Add()
            comparison_sheet.Name = "快速优化结果"
            
            # 写入表头
            headers = ["排名", "方案名称", "Y_predict", "提升", "提升比例"] + self.channels
            for i, header in enumerate(headers, 1):
                comparison_sheet.Cells(1, i).Value = header
                comparison_sheet.Cells(1, i).Font.Bold = True
            
            # 写入数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                comparison_sheet.Cells(row, 1).Value = rank
                comparison_sheet.Cells(row, 2).Value = result["方案名称"]
                comparison_sheet.Cells(row, 3).Value = result["Y_predict"]
                comparison_sheet.Cells(row, 4).Value = result["提升"]
                comparison_sheet.Cells(row, 5).Value = result["提升比例"]
                
                # 写入各渠道预算配比
                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / self.total_budget
                    comparison_sheet.Cells(row, 6 + i).Value = ratio
                    comparison_sheet.Cells(row, 6 + i).NumberFormat = "0.00%"
            
            comparison_sheet.Columns.AutoFit()
            self.workbook.Save()
            print(f"\n结果已导出到Excel文件的'快速优化结果'工作表")
            
        except Exception as e:
            print(f"导出Excel时出错: {e}")
        
        # 显示分析报告
        self.print_analysis_report(results, baseline_y)
    
    def print_analysis_report(self, results, baseline_y):
        """打印详细的分析报告"""
        print("\n" + "=" * 80)
        print("详细分析报告")
        print("=" * 80)
        
        print(f"\n基准方案（平均分配）Y_predict: {baseline_y:,.2f}")
        print(f"总预算: {self.total_budget:,}")
        
        print(f"\n所有方案对比:")
        print("-" * 80)
        print(f"{'排名':<4} {'方案名称':<20} {'Y_predict':<12} {'提升':<10} {'提升比例':<8}")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['方案名称']:<20} {result['Y_predict']:>10,.0f} {result['提升']:>8,.0f} {result['提升比例']:>7}")
        
        # 最优方案详细分析
        if len(results) > 1:  # 排除基准方案
            best_result = results[0] if results[0]["方案名称"] != "基准方案（平均分配）" else results[1]
            print(f"\n最优方案详细分析: {best_result['方案名称']}")
            print("-" * 50)
            
            # 按预算从高到低排序
            budget_items = [(channel, budget) for channel, budget in best_result["预算分配"].items()]
            budget_items.sort(key=lambda x: x[1], reverse=True)
            
            print("渠道预算分配:")
            for channel, budget in budget_items:
                ratio = budget / self.total_budget
                if ratio > 0.01:  # 只显示占比超过1%的渠道
                    print(f"  {channel:<15}: {ratio:>6.1%} ({budget:>10,.0f})")
        
        # 输出原始数据供验证
        print(f"\n" + "=" * 80)
        print("原始输入数据（供Excel验证）")
        print("=" * 80)
        
        for i, result in enumerate(results[:3], 1):
            print(f"\n方案{i}: {result['方案名称']}")
            print("C38:R57区域输入值（每行相同）:")
            budgets = [result["预算分配"][ch] for ch in self.channels]
            budget_str = ", ".join([f"{budget:.2f}" for budget in budgets])
            print(f"[{budget_str}]")
    
    def cleanup(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass


def main():
    """主函数"""
    excel_file = "Gatorade simulation tool_cal.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 {excel_file}")
        return
    
    optimizer = GatoradeFastOptimizer(excel_file)
    
    try:
        # 初始化Excel
        if not optimizer.initialize_excel():
            print("Excel初始化失败，程序退出")
            return
        
        # 运行快速优化分析
        start_time = datetime.now()
        print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results, baseline_y = optimizer.run_fast_optimization()
        
        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")
        
        # 导出结果并显示报告
        optimizer.export_and_display_results(results, baseline_y)
        
        print(f"\n优化完成！请查看Excel文件中的'快速优化结果'工作表。")
        
    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        optimizer.cleanup()
        print("\n资源清理完成")


if __name__ == "__main__":
    main()
