@echo off
chcp 65001 >nul
echo 🚀 媒体预算优化工具 - 快速打包脚本
echo ============================================================

echo 📦 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo 📦 安装PyInstaller...
python -m pip install pyinstaller openpyxl pandas numpy

echo 🔨 开始打包EXE文件...
python -m PyInstaller --onefile --windowed --name="媒体预算优化工具" --add-data="*.py;." --add-data="*.xlsx;." universal_media_optimizer_v2.py

if exist "dist\媒体预算优化工具.exe" (
    echo ✅ EXE文件生成成功！
    echo 📁 文件位置: dist\媒体预算优化工具.exe
    
    echo 📋 复制相关文件...
    if exist "Gatorade simulation tool_cal.xlsx" (
        copy "Gatorade simulation tool_cal.xlsx" "dist\"
        echo ✅ 复制Excel示例文件
    )
    
    echo 📝 创建使用说明...
    echo 媒体预算优化工具 - 使用说明 > "dist\使用说明.txt"
    echo. >> "dist\使用说明.txt"
    echo 1. 双击"媒体预算优化工具.exe"启动程序 >> "dist\使用说明.txt"
    echo 2. 配置Excel文件路径和优化参数 >> "dist\使用说明.txt"
    echo 3. 点击"开始优化"运行优化算法 >> "dist\使用说明.txt"
    echo 4. 查看生成的优化结果报告 >> "dist\使用说明.txt"
    echo. >> "dist\使用说明.txt"
    echo 注意：首次运行可能需要较长时间，请耐心等待 >> "dist\使用说明.txt"
    
    echo.
    echo 🎉 打包完成！
    echo 📁 所有文件已保存到 dist 目录
    echo 💡 您可以将 dist 目录中的文件分发给其他用户
    
) else (
    echo ❌ EXE文件生成失败
    echo 请检查错误信息并重试
)

echo.
echo 🧹 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "媒体预算优化工具.spec" del "媒体预算优化工具.spec"

echo ✅ 清理完成
echo.
pause
