import win32com.client
import os
import time

def test_kpi_calculation():
    """验证KPI计算是否正确"""
    excel_path = os.path.abspath("Gatorade simulation tool_cal.xlsx")
    
    print("正在验证KPI计算...")
    print(f"Excel文件: {excel_path}")
    
    try:
        # 初始化Excel
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = True  # 设置为可见，方便观察
        excel.DisplayAlerts = False
        
        # 打开工作簿
        workbook = excel.Workbooks.Open(excel_path)
        worksheet = workbook.Worksheets("calculation")
        
        print("\n=== 测试1: 每个单元格输入3125 ===")
        
        # 清空C38:R57区域
        range_to_clear = worksheet.Range("C38:R57")
        range_to_clear.ClearContents()
        
        # 输入3125到每个单元格
        for row in range(38, 58):  # 38到57，共20行
            for col in range(3, 19):  # C到R，共16列
                worksheet.Cells(row, col).Value = 3125
        
        print("已输入数据：每个单元格 = 3125")
        print("总预算 = 16渠道 × 3125 × 20行 = 1,000,000")
        
        # 强制重新计算
        worksheet.Calculate()
        time.sleep(2)  # 等待计算完成
        
        # 读取U38:U57的值
        print("\nU38:U57的Y_predict值:")
        total_y = 0
        for row in range(38, 58):
            y_value = worksheet.Cells(row, 21).Value  # U列是第21列
            if y_value is not None:
                total_y += float(y_value)
                print(f"U{row}: {y_value}")
            else:
                print(f"U{row}: None")
        
        print(f"\nY_predict总和: {total_y:,.2f}")
        
        # 验证总预算
        total_input = 0
        for row in range(38, 58):
            for col in range(3, 19):
                value = worksheet.Cells(row, col).Value
                if value is not None:
                    total_input += float(value)
        
        print(f"输入总预算验证: {total_input:,.0f}")
        
        print("\n=== 测试2: 检查公式 ===")
        
        # 检查U38单元格的公式
        u38_formula = worksheet.Cells(38, 21).Formula
        print(f"U38单元格公式: {u38_formula}")
        
        # 检查几个关键单元格的值
        print(f"\nC38值: {worksheet.Cells(38, 3).Value}")
        print(f"R38值: {worksheet.Cells(38, 18).Value}")
        print(f"U38值: {worksheet.Cells(38, 21).Value}")
        
        # 保存文件
        workbook.Save()
        
        print(f"\n=== 结论 ===")
        print(f"您手动输入得到的KPI: 15,093")
        print(f"程序计算得到的KPI: {total_y:,.2f}")
        
        if abs(total_y - 15093) < 100:
            print("✅ KPI计算一致！")
        else:
            print("❌ KPI计算不一致，需要检查原因")
            print("可能原因：")
            print("1. Excel公式计算延迟")
            print("2. 数据输入方式不同")
            print("3. 工作表状态不同")
        
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            # 不关闭Excel，让用户可以查看
            print(f"\nExcel保持打开状态，请手动检查数据")
            print("按任意键继续...")
            input()
            
            workbook.Close(SaveChanges=True)
            excel.Quit()
        except:
            pass

if __name__ == "__main__":
    test_kpi_calculation()
