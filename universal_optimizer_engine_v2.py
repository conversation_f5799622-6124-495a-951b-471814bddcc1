import win32com.client
import os
import time
import random
from datetime import datetime

class UniversalOptimizerEngineV2:
    def __init__(self, config, channels, progress_callback=None):
        self.config = config
        self.channels = channels
        self.progress_callback = progress_callback
        self.excel = None
        self.workbook = None
        self.worksheet = None

        # 控制状态
        self.is_paused = False
        self.is_terminated = False
        self.pause_event = None
        
        # 从配置中获取参数
        self.excel_file = config["excel_file"]
        self.worksheet_name = config["worksheet_name"]
        self.input_start_row = config["input_start_row"]
        self.input_end_row = config["input_end_row"]
        self.input_start_col = config["input_start_col"]
        self.input_end_col = config["input_end_col"]
        self.output_col = config["output_col"]
        self.output_start_row = config.get("output_start_row", config["input_start_row"])
        self.output_end_row = config.get("output_end_row", config["input_end_row"])
        self.total_budget = config["total_budget"]
        self.optimization_schemes = config["optimization_schemes"]
        self.baseline_allocation = config["baseline_allocation"]
        self.custom_baseline_ratios = config.get("custom_baseline_ratios", {})
        self.channel_constraints = config.get("channel_constraints", {})
        
        # 默认约束范围：2%-60%
        self.default_min_ratio = 0.02
        self.default_max_ratio = 0.60

    def pause_optimization(self):
        """暂停优化"""
        self.is_paused = True
        if self.progress_callback:
            self.progress_callback(-1, "已暂停", "优化已暂停，点击恢复继续")

    def resume_optimization(self):
        """恢复优化"""
        self.is_paused = False
        if self.progress_callback:
            self.progress_callback(-1, "已恢复", "优化已恢复")

    def terminate_optimization(self):
        """终止优化"""
        self.is_terminated = True
        if self.progress_callback:
            self.progress_callback(-1, "已终止", "优化已被用户终止")

    def check_control_state(self):
        """检查控制状态"""
        # 检查是否被终止
        if self.is_terminated:
            return "terminated"

        # 检查是否暂停
        while self.is_paused and not self.is_terminated:
            time.sleep(0.1)  # 暂停时等待

        return "running" if not self.is_terminated else "terminated"
        
    def initialize_excel(self):
        """初始化Excel连接"""
        try:
            # 先尝试关闭可能存在的Excel进程
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
                time.sleep(1)
            except:
                pass

            # 创建新的Excel应用实例
            self.excel = win32com.client.Dispatch("Excel.Application")

            # 设置Excel属性（分步设置，便于调试）
            try:
                self.excel.DisplayAlerts = False
                self.excel.ScreenUpdating = False
                self.excel.Visible = False
            except Exception as e:
                print(f"设置Excel属性时出现警告: {e}")
                # 继续执行，这些属性设置失败通常不影响核心功能

            # 打开工作簿
            self.workbook = self.excel.Workbooks.Open(os.path.abspath(self.excel_file))
            self.worksheet = self.workbook.Worksheets(self.worksheet_name)

            print(f"Excel连接成功，工作表: {self.worksheet_name}")
            return True

        except Exception as e:
            print(f"初始化Excel失败: {e}")
            print("可能的解决方案:")
            print("1. 确保Excel文件未被其他程序占用")
            print("2. 检查文件路径是否正确")
            print("3. 确保Excel已正确安装")
            return False
    
    def cleanup_excel(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass
    
    def apply_constraints(self, budgets):
        """应用渠道约束"""
        constrained_budgets = budgets.copy()
        
        for i, channel in enumerate(self.channels):
            if channel in self.channel_constraints:
                constraint = self.channel_constraints[channel]
                min_ratio = constraint["min"]
                max_ratio = constraint["max"]
            else:
                # 使用默认约束
                min_ratio = self.default_min_ratio
                max_ratio = self.default_max_ratio
            
            min_budget = self.total_budget * min_ratio
            max_budget = self.total_budget * max_ratio
            
            constrained_budgets[i] = max(min_budget, min(max_budget, constrained_budgets[i]))
        
        # 重新归一化到总预算
        total_constrained = sum(constrained_budgets)
        if total_constrained > 0:
            constrained_budgets = [b * self.total_budget / total_constrained for b in constrained_budgets]
        
        return constrained_budgets

    def get_constraint_summary(self):
        """获取约束设置摘要"""
        if not self.channel_constraints:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有渠道"

        custom_count = len(self.channel_constraints)
        default_count = len(self.channels) - custom_count

        if custom_count == 0:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有{len(self.channels)}个渠道"
        elif default_count == 0:
            return f"所有{len(self.channels)}个渠道使用自定义约束"
        else:
            return f"{custom_count}个渠道使用自定义约束，{default_count}个渠道使用默认约束({self.default_min_ratio:.1%}-{self.default_max_ratio:.1%})"

    def get_channel_categories(self):
        """获取渠道分类（可适配不同项目）"""
        # 通用渠道分类规则（基于关键词匹配）
        categories = {
            "数字媒体": {
                "keywords": ["Search", "Display", "RTB", "Digital", "SEM", "SEO", "Programmatic"],
                "description": "精准投放，数据驱动"
            },
            "社交媒体": {
                "keywords": ["KOL", "Social", "Weibo", "Douyin", "Red", "WeChat", "TikTok", "Instagram", "Facebook"],
                "description": "社交互动，口碑传播"
            },
            "视频广告": {
                "keywords": ["OTT", "OTV", "Video", "Preroll", "YouTube", "iQiyi", "Youku"],
                "description": "视觉冲击，品牌故事"
            },
            "线下媒体": {
                "keywords": ["LCD", "Metro", "Outdoor", "Billboard", "Transit", "Cinema", "Radio"],
                "description": "广泛覆盖，品牌曝光"
            },
            "赞助营销": {
                "keywords": ["Sponsor", "Event", "Drama", "Sports", "Entertainment", "Partnership"],
                "description": "品牌联想，情感连接"
            },
            "电商平台": {
                "keywords": ["Tmall", "JD", "Taobao", "Amazon", "Ecommerce", "Shop"],
                "description": "直接转化，销售驱动"
            },
            "内容营销": {
                "keywords": ["Content", "Native", "Article", "Blog", "News"],
                "description": "软性植入，价值传递"
            },
            "移动应用": {
                "keywords": ["App", "Mobile", "In-App", "Game", "Push"],
                "description": "移动优先，即时触达"
            }
        }

        # 根据实际渠道名称进行分类
        channel_classification = {}
        for channel in self.channels:
            classified = False
            for category, info in categories.items():
                for keyword in info["keywords"]:
                    if keyword.lower() in channel.lower():
                        if category not in channel_classification:
                            channel_classification[category] = []
                        channel_classification[category].append(channel)
                        classified = True
                        break
                if classified:
                    break

            # 如果没有匹配到分类，归入"其他媒体"
            if not classified:
                if "其他媒体" not in channel_classification:
                    channel_classification["其他媒体"] = []
                channel_classification["其他媒体"].append(channel)

        return channel_classification, categories

    def generate_scheme_description(self, scheme_name, budget_allocation):
        """根据预算分配生成方案描述"""
        channel_classification, _ = self.get_channel_categories()

        # 计算各类型渠道的预算占比
        category_ratios = {}
        for category, channels_in_cat in channel_classification.items():
            total_budget_in_cat = sum(budget_allocation.get(ch, 0) for ch in channels_in_cat)
            ratio = total_budget_in_cat / self.total_budget
            category_ratios[category] = ratio

        # 按预算占比排序
        sorted_categories = sorted(category_ratios.items(), key=lambda x: x[1], reverse=True)

        # 生成描述
        if "基准方案" in scheme_name:
            if "平均分配" in scheme_name:
                return "所有渠道平均分配预算，均衡覆盖各媒体类型，适合品牌认知建设阶段"
            else:
                return "基于自定义配比分配预算，体现特定媒体策略偏好"

        # 分析主要投放策略
        top_categories = [cat for cat, ratio in sorted_categories[:3] if ratio > 0.15]  # 占比超过15%的主要类型

        description_parts = []

        # 主要策略描述
        if len(top_categories) >= 2:
            main_cats = top_categories[:2]
            main_ratios = [category_ratios[cat] for cat in main_cats]

            if "数字媒体" in main_cats and "社交媒体" in main_cats:
                description_parts.append("数字化营销导向")
                if sum(main_ratios) > 0.6:
                    description_parts.append("重点布局数字媒体和社交平台")
                else:
                    description_parts.append("平衡数字媒体和社交互动")

            elif "线下媒体" in main_cats or "视频广告" in main_cats:
                if category_ratios.get("线下媒体", 0) + category_ratios.get("视频广告", 0) > 0.4:
                    description_parts.append("传统媒体主导")
                    description_parts.append("注重品牌曝光和覆盖面")
                else:
                    description_parts.append("传统与数字媒体并重")

            elif "赞助营销" in main_cats:
                description_parts.append("品牌营销导向")
                description_parts.append("通过赞助活动建立品牌联想")

        # 具体渠道特色
        top_channels = sorted(budget_allocation.items(), key=lambda x: x[1], reverse=True)[:3]
        high_budget_channels = [ch for ch, budget in top_channels if budget / self.total_budget > 0.2]

        if high_budget_channels:
            channel_descriptions = {
                "SearchVolume": "搜索营销",
                "DouyinKOL": "抖音KOL",
                "RedKOL": "小红书KOL",
                "WeiboKOL": "微博KOL",
                "OTTPreroll": "OTT视频",
                "Display": "展示广告",
                "RTBOCPX": "程序化购买"
            }

            featured_channels = []
            for ch in high_budget_channels:
                if ch in channel_descriptions:
                    featured_channels.append(channel_descriptions[ch])
                else:
                    featured_channels.append(ch)

            if featured_channels:
                description_parts.append(f"重点投放{'/'.join(featured_channels)}")

        # 投放目标描述
        digital_ratio = category_ratios.get("数字媒体", 0) + category_ratios.get("社交媒体", 0)
        traditional_ratio = category_ratios.get("线下媒体", 0) + category_ratios.get("视频广告", 0)

        if digital_ratio > 0.7:
            description_parts.append("追求精准触达和效果转化")
        elif traditional_ratio > 0.4:
            description_parts.append("强化品牌认知和市场覆盖")
        elif digital_ratio > 0.4 and traditional_ratio > 0.2:
            description_parts.append("兼顾品牌建设与效果转化")

        # 组合描述
        if description_parts:
            return "，".join(description_parts)
        else:
            return "多元化媒体组合，平衡各类型渠道投入"
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算并计算KPI"""
        try:
            # 应用约束
            constrained_budgets = self.apply_constraints(budgets)
            
            # 清空输入区域
            input_range = self.worksheet.Range(
                self.worksheet.Cells(self.input_start_row, self.input_start_col),
                self.worksheet.Cells(self.input_end_row, self.input_end_col)
            )
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.input_end_row - self.input_start_row + 1
            budgets_per_row = [budget / num_rows for budget in constrained_budgets]
            
            # 写入预算数据
            for row in range(self.input_start_row, self.input_end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_start_col + i
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.3)
            
            # 读取输出值并求和
            total_kpi = 0
            for row in range(self.output_start_row, self.output_end_row + 1):
                kpi_value = self.worksheet.Cells(row, self.output_col).Value
                if kpi_value is not None:
                    total_kpi += float(kpi_value)
            
            return total_kpi, constrained_budgets
        except Exception as e:
            print(f"计算KPI时出错: {e}")
            return 0, budgets
    
    def get_baseline_kpi(self):
        """获取基准方案的KPI"""
        if self.baseline_allocation == "equal":
            # 平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        elif self.baseline_allocation == "custom":
            # 自定义基准方案
            if not self.custom_baseline_ratios:
                raise ValueError("自定义基准配比未设置")
            
            equal_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0  # 转换为小数
                    equal_budgets.append(self.total_budget * ratio)
                else:
                    # 如果某个渠道没有设置，使用平均值
                    equal_budgets.append(self.total_budget / len(self.channels))
        else:
            # 默认平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        baseline_kpi, constrained_baseline = self.write_budgets_and_calculate(equal_budgets)
        return baseline_kpi, constrained_baseline
    
    def test_individual_channels(self, baseline_kpi):
        """测试各渠道单独表现"""
        channel_performance = []
        
        for i, channel in enumerate(self.channels):
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            kpi, _ = self.write_budgets_and_calculate(test_budgets)
            improvement = kpi - baseline_kpi
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'kpi': kpi,
                'improvement': improvement,
                'efficiency': improvement / (self.total_budget * 0.3)
            })
        
        # 按改进效果排序
        channel_performance.sort(key=lambda x: x['improvement'], reverse=True)
        return channel_performance
    
    def generate_business_description(self, budgets, scheme_type="custom"):
        """根据预算分配生成业务导向的方案描述"""
        # 定义渠道分类
        channel_categories = {
            "数字媒体": ["SearchVolume", "Display", "RTBOCPX", "DigitalCoop"],
            "社交媒体": ["DouyinKOL", "RedKOL", "WeiboKOL", "DouyinPush", "WeiboPush"],
            "视频广告": ["OTTPreroll", "OTVPreroll"],
            "线下媒体": ["BuildingLCD", "Metro", "CreativeOutdoor"],
            "赞助营销": ["SponsorEvent", "SponsorDrama"]
        }

        # 计算各类型渠道的预算占比
        category_ratios = {}
        for category, channels_in_cat in channel_categories.items():
            total_budget_in_cat = sum(budgets[self.channels.index(ch)] for ch in channels_in_cat if ch in self.channels)
            ratio = total_budget_in_cat / self.total_budget
            category_ratios[category] = ratio

        # 计算数字化比例
        digital_ratio = category_ratios.get("数字媒体", 0) + category_ratios.get("社交媒体", 0)

        # 找出主导渠道类型
        dominant_category = max(category_ratios.items(), key=lambda x: x[1])

        # 生成业务描述
        if scheme_type == "baseline_equal":
            return "均衡配置策略，各渠道平均分配预算，适合品牌初期测试和全渠道覆盖"
        elif scheme_type == "baseline_custom":
            return "定制化基准策略，基于历史经验和品牌特点的预算配比方案"

        # 根据数字化比例生成描述
        if digital_ratio >= 0.7:
            digital_desc = "数字化优先策略"
            focus_desc = "重点投放数字媒体和社交平台，适合年轻消费群体和电商转化目标"
        elif digital_ratio >= 0.5:
            digital_desc = "数字化主导策略"
            focus_desc = "平衡数字媒体与传统媒体，兼顾品牌曝光和精准转化"
        elif digital_ratio >= 0.3:
            digital_desc = "传统数字并重策略"
            focus_desc = "传统媒体为主，数字媒体补充，适合成熟品牌和大众市场"
        else:
            digital_desc = "传统媒体主导策略"
            focus_desc = "以传统媒体为核心，强化品牌认知和大众覆盖"

        # 根据主导渠道类型添加细节
        if dominant_category[0] == "社交媒体" and dominant_category[1] > 0.4:
            detail_desc = "，强化KOL合作和社交互动，提升品牌话题度和用户参与"
        elif dominant_category[0] == "数字媒体" and dominant_category[1] > 0.3:
            detail_desc = "，优化搜索营销和程序化投放，提升转化效率和ROI"
        elif dominant_category[0] == "视频广告" and dominant_category[1] > 0.2:
            detail_desc = "，加强视频内容营销，提升品牌故事传播和情感连接"
        elif dominant_category[0] == "线下媒体" and dominant_category[1] > 0.2:
            detail_desc = "，强化线下场景触达，提升品牌可见度和消费者接触频次"
        else:
            detail_desc = "，实现多渠道协同效应，最大化媒体投资回报"

        return f"{digital_desc}：{focus_desc}{detail_desc}"

    def generate_optimization_schemes(self, baseline_kpi, channel_performance):
        """生成优化方案"""
        results = []

        # 添加基准方案
        if self.baseline_allocation == "equal":
            baseline_name = "基准方案（平均分配）"
            baseline_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        else:
            baseline_name = "基准方案（自定义配比）"
            baseline_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0
                    baseline_budgets.append(self.total_budget * ratio)
                else:
                    baseline_budgets.append(self.total_budget / len(self.channels))

        baseline_budget_dict = dict(zip(self.channels, baseline_budgets))
        baseline_desc = self.generate_scheme_description(baseline_name, baseline_budget_dict)

        results.append({
            "方案名称": baseline_name,
            "方案描述": baseline_desc,
            "KPI": baseline_kpi,
            "预算分配": baseline_budget_dict,
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 获取高效渠道
        top_channels = [perf['channel'] for perf in channel_performance[:6] if perf['improvement'] > 0]
        
        if len(top_channels) >= 2:
            # 方案1: 高效渠道集中
            budgets = [0.0] * len(self.channels)
            high_budget = self.total_budget * 0.6 / len(top_channels[:3])
            low_budget = self.total_budget * 0.4 / (len(self.channels) - 3)
            
            for i, channel in enumerate(self.channels):
                if channel in top_channels[:3]:
                    budgets[i] = high_budget
                else:
                    budgets[i] = low_budget
            
            kpi, final_budgets = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi
            improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0

            scheme_name = "高效渠道集中方案"
            final_budget_dict = dict(zip(self.channels, final_budgets))
            scheme_desc = self.generate_scheme_description(scheme_name, final_budget_dict)

            results.append({
                "方案名称": scheme_name,
                "方案描述": scheme_desc,
                "KPI": kpi,
                "预算分配": final_budget_dict,
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
        
        # 方案2: 渐进式优化
        budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        for i, channel in enumerate(self.channels):
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf:
                rank = channel_performance.index(perf) + 1
                if rank <= 3:
                    budgets[i] *= 1.5
                elif rank <= 6:
                    budgets[i] *= 1.2
                elif rank > 10:
                    budgets[i] *= 0.7
        
        # 重新归一化
        total_check = sum(budgets)
        budgets = [b * self.total_budget / total_check for b in budgets]
        
        kpi, final_budgets = self.write_budgets_and_calculate(budgets)
        improvement = kpi - baseline_kpi
        improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0

        scheme_name = "渐进式优化方案"
        final_budget_dict = dict(zip(self.channels, final_budgets))
        scheme_desc = self.generate_scheme_description(scheme_name, final_budget_dict)

        results.append({
            "方案名称": scheme_name,
            "方案描述": scheme_desc,
            "KPI": kpi,
            "预算分配": final_budget_dict,
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        
        # 方案3-N: 智能搜索优化（使用用户设置的迭代次数）
        best_random_results = []
        max_iterations = max(self.config.get("max_iterations", 200), self.optimization_schemes * 20)

        print(f"正在进行智能搜索优化（{max_iterations}次迭代）...")

        best_kpi_so_far = baseline_kpi
        no_improvement_count = 0

        for iteration in range(max_iterations):
            # 检查控制状态
            control_state = self.check_control_state()
            if control_state == "terminated":
                print(f"  优化被用户终止（{iteration}/{max_iterations}）")
                break

            # 显示进度
            if iteration % 20 == 0:
                progress = (iteration / max_iterations) * 100
                status = f"智能搜索优化 ({iteration}/{max_iterations})"
                detail = f"已找到 {len(best_random_results)} 个有效方案"

                print(f"  搜索进度: {progress:.1f}% ({iteration}/{max_iterations})")

                # 回调UI更新进度
                if self.progress_callback:
                    self.progress_callback(progress, status, detail)

            # 生成随机权重，偏向高效渠道
            weights = [random.uniform(0.1, 3.0) for _ in self.channels]

            # 根据渠道表现调整权重
            for i, channel in enumerate(self.channels):
                perf = next((p for p in channel_performance if p['channel'] == channel), None)
                if perf and perf['improvement'] > 0:
                    rank = channel_performance.index(perf) + 1
                    if rank <= 3:
                        weights[i] *= 2.5  # 增加权重倍数
                    elif rank <= 6:
                        weights[i] *= 1.8
                    elif rank <= 10:
                        weights[i] *= 1.2

            # 添加一些随机性避免局部最优
            if iteration > max_iterations * 0.3:  # 后期增加更多随机性
                for i in range(len(weights)):
                    weights[i] *= random.uniform(0.8, 1.2)

            # 归一化权重
            total_weight = sum(weights)
            ratios = [w / total_weight for w in weights]
            budgets = [r * self.total_budget for r in ratios]

            kpi, final_budgets = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi

            # 记录好的结果
            if improvement > 0:
                improvement_pct = (improvement / baseline_kpi) * 100

                # 检查是否是新的最佳结果
                if kpi > best_kpi_so_far:
                    best_kpi_so_far = kpi
                    no_improvement_count = 0
                    print(f"    发现更好结果: KPI = {kpi:,.2f} (+{improvement_pct:.2f}%)")
                else:
                    no_improvement_count += 1

                # 只保存足够好的结果
                if improvement_pct > 0.5:  # 至少0.5%的提升
                    scheme_name = f"智能搜索方案{len(best_random_results)+1}"
                    final_budget_dict = dict(zip(self.channels, final_budgets))
                    scheme_desc = self.generate_scheme_description(scheme_name, final_budget_dict)

                    best_random_results.append({
                        "方案名称": scheme_name,
                        "方案描述": scheme_desc,
                        "KPI": kpi,
                        "预算分配": final_budget_dict,
                        "提升": improvement,
                        "提升比例": f"{improvement_pct:.2f}%"
                    })

            # 早停机制：如果很长时间没有改进，提前结束
            if no_improvement_count > 100 and len(best_random_results) >= self.optimization_schemes:
                print(f"  提前结束搜索（{iteration+1}次迭代后无显著改进）")
                break

        print(f"智能搜索完成，找到 {len(best_random_results)} 个有效方案")

        # 按KPI排序，取前几个
        best_random_results.sort(key=lambda x: x["KPI"], reverse=True)
        results.extend(best_random_results[:self.optimization_schemes-2])
        
        # 按KPI排序所有结果
        results.sort(key=lambda x: x["KPI"], reverse=True)

        return results

    def export_results_simple(self, results, baseline_kpi, channel_performance):
        """简化版导出结果（避免Excel COM复杂性）"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"媒体预算优化结果_v2_{timestamp}.txt"

            print(f"正在导出结果到: {output_filename}")

            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("媒体预算优化分析报告 v2.0\n")
                f.write("=" * 80 + "\n\n")

                # 基本信息
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"基准方案KPI: {baseline_kpi:,.2f}\n")
                f.write(f"总预算: ¥{self.total_budget:,}\n")

                # 约束信息
                constraint_info = self.get_constraint_summary()
                f.write(f"约束设置: {constraint_info}\n")
                f.write(f"基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}\n\n")

                # 优化结果
                f.write("优化结果总览:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{'排名':<4} {'方案名称':<20} {'KPI':<12} {'提升':<12} {'提升比例':<10}\n")
                f.write("-" * 80 + "\n")

                for rank, result in enumerate(results, 1):
                    f.write(f"{rank:<4} {result['方案名称']:<20} {result['KPI']:<12,.0f} "
                           f"{result['提升']:<12,.0f} {result['提升比例']:<10}\n")

                # 详细预算分配
                f.write(f"\n\n详细预算分配:\n")
                f.write("-" * 80 + "\n")

                for rank, result in enumerate(results, 1):
                    f.write(f"\n{rank}. {result['方案名称']} (KPI: {result['KPI']:,.0f}, 提升: {result['提升比例']})\n")
                    f.write("-" * 60 + "\n")

                    # 按预算排序显示渠道
                    budget_items = [(ch, budget) for ch, budget in result['预算分配'].items()]
                    budget_items.sort(key=lambda x: x[1], reverse=True)

                    for channel, budget in budget_items:
                        ratio = budget / self.total_budget
                        f.write(f"  {channel:<20}: {ratio:>6.1%} (¥{budget:>10,.0f})\n")

                # 渠道效率分析
                f.write(f"\n\n渠道效率分析:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{'排名':<4} {'渠道名称':<20} {'测试KPI':<12} {'vs基准提升':<12} {'效率':<10}\n")
                f.write("-" * 80 + "\n")

                for rank, perf in enumerate(channel_performance, 1):
                    f.write(f"{rank:<4} {perf['channel']:<20} {perf['kpi']:<12,.0f} "
                           f"{perf['improvement']:<12,.0f} {perf['efficiency']:<10,.2f}\n")

            print(f"结果已导出到: {output_filename}")
            return output_filename

        except Exception as e:
            print(f"导出结果时出错: {e}")
            return None

    def export_results(self, results, baseline_kpi, channel_performance):
        """导出结果到文件（优先Excel，失败则文本）"""
        # 首先尝试简化版导出作为备份
        text_file = self.export_results_simple(results, baseline_kpi, channel_performance)

        # 尝试Excel导出
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"媒体预算优化结果_v2_{timestamp}.xlsx"

            print(f"正在尝试导出Excel文件: {output_filename}")

            # 使用现有的Excel实例（避免创建新实例）
            if hasattr(self, 'excel') and self.excel:
                excel_app = self.excel
            else:
                excel_app = win32com.client.Dispatch("Excel.Application")
                excel_app.Visible = False
                excel_app.DisplayAlerts = False

            # 创建新工作簿
            new_workbook = excel_app.Workbooks.Add()

            # 工作表1: 优化结果总览
            overview_sheet = new_workbook.Worksheets(1)
            overview_sheet.Name = "优化结果总览"

            # 写入基本信息
            overview_sheet.Cells(1, 1).Value = "媒体预算优化分析报告 v2.0"
            overview_sheet.Cells(1, 1).Font.Size = 14
            overview_sheet.Cells(1, 1).Font.Bold = True

            overview_sheet.Cells(2, 1).Value = f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            overview_sheet.Cells(3, 1).Value = f"基准KPI: {baseline_kpi:,.2f}"
            overview_sheet.Cells(4, 1).Value = f"总预算: ¥{self.total_budget:,}"

            constraint_info = self.get_constraint_summary()
            overview_sheet.Cells(5, 1).Value = f"约束设置: {constraint_info}"

            # 写入结果表头
            headers = ["排名", "方案名称", "KPI", "提升", "提升比例", "方案描述"]
            for i, header in enumerate(headers, 1):
                cell = overview_sheet.Cells(7, i)
                cell.Value = header
                cell.Font.Bold = True

            # 写入结果数据
            for rank, result in enumerate(results, 1):
                row = rank + 7
                overview_sheet.Cells(row, 1).Value = rank
                overview_sheet.Cells(row, 2).Value = result["方案名称"]
                overview_sheet.Cells(row, 3).Value = result["KPI"]
                overview_sheet.Cells(row, 4).Value = result["提升"]
                overview_sheet.Cells(row, 5).Value = result["提升比例"]
                overview_sheet.Cells(row, 6).Value = result["方案描述"]

            # 工作表2: 详细预算分配
            detail_sheet = new_workbook.Worksheets.Add()
            detail_sheet.Name = "详细预算分配"

            # 表头
            detail_headers = ["方案名称", "KPI", "提升比例"] + self.channels
            for i, header in enumerate(detail_headers, 1):
                cell = detail_sheet.Cells(1, i)
                cell.Value = header
                cell.Font.Bold = True

            # 数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                detail_sheet.Cells(row, 1).Value = result["方案名称"]
                detail_sheet.Cells(row, 2).Value = result["KPI"]
                detail_sheet.Cells(row, 3).Value = result["提升比例"]

                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / self.total_budget
                    detail_sheet.Cells(row, 4 + i).Value = ratio
                    detail_sheet.Cells(row, 4 + i).NumberFormat = "0.00%"

            # 工作表3: 原始输入数据
            raw_data_sheet = new_workbook.Worksheets.Add()
            raw_data_sheet.Name = "原始输入数据"

            raw_data_sheet.Cells(1, 1).Value = "原始输入数据（可直接复制到Excel验证）"
            raw_data_sheet.Cells(1, 1).Font.Bold = True
            raw_data_sheet.Cells(2, 1).Value = f"输入区域: {chr(64+self.input_start_col)}{self.input_start_row}:{chr(64+self.input_end_col)}{self.input_end_row}"
            raw_data_sheet.Cells(3, 1).Value = f"输出区域: {chr(64+self.output_col)}{self.output_start_row}:{chr(64+self.output_col)}{self.output_end_row}"

            # 表头
            raw_data_sheet.Cells(5, 1).Value = "方案名称"
            for i, channel in enumerate(self.channels, 2):
                raw_data_sheet.Cells(5, i).Value = channel

            # 每个方案的原始数据
            for rank, result in enumerate(results, 1):
                row = rank + 5
                raw_data_sheet.Cells(row, 1).Value = result["方案名称"]

                # 计算每行的预算分配
                budgets = [result["预算分配"][ch] for ch in self.channels]
                num_rows = self.input_end_row - self.input_start_row + 1
                budgets_per_row = [budget / num_rows for budget in budgets]

                for i, budget_per_row in enumerate(budgets_per_row, 2):
                    raw_data_sheet.Cells(row, i).Value = budget_per_row
                    raw_data_sheet.Cells(row, i).NumberFormat = "0.00"

            # 工作表4: 业务对比分析
            analysis_sheet = new_workbook.Worksheets.Add()
            analysis_sheet.Name = "业务对比分析"

            analysis_sheet.Cells(1, 1).Value = "广告业务对比分析"
            analysis_sheet.Cells(1, 1).Font.Size = 14
            analysis_sheet.Cells(1, 1).Font.Bold = True

            # 渠道分类分析
            analysis_sheet.Cells(3, 1).Value = "渠道类型分析"
            analysis_sheet.Cells(3, 1).Font.Bold = True

            # 使用通用渠道分类系统
            channel_categories, _ = self.get_channel_categories()

            # 分析表头
            analysis_headers = ["方案名称"] + list(channel_categories.keys()) + ["数字化比例", "传统媒体比例"]
            for i, header in enumerate(analysis_headers, 1):
                cell = analysis_sheet.Cells(5, i)
                cell.Value = header
                cell.Font.Bold = True

            # 分析每个方案的渠道分布
            for rank, result in enumerate(results, 1):
                row = rank + 5
                analysis_sheet.Cells(row, 1).Value = result["方案名称"]

                # 计算各类型渠道的预算占比
                category_budgets = {}
                for category, channels_in_cat in channel_categories.items():
                    total_budget_in_cat = sum(result["预算分配"].get(ch, 0) for ch in channels_in_cat if ch in result["预算分配"])
                    ratio = total_budget_in_cat / self.total_budget
                    category_budgets[category] = ratio
                    analysis_sheet.Cells(row, list(channel_categories.keys()).index(category) + 2).Value = ratio
                    analysis_sheet.Cells(row, list(channel_categories.keys()).index(category) + 2).NumberFormat = "0.00%"

                # 计算数字化比例（数字媒体+社交媒体）
                digital_ratio = category_budgets.get("数字媒体", 0) + category_budgets.get("社交媒体", 0)
                analysis_sheet.Cells(row, len(channel_categories) + 2).Value = digital_ratio
                analysis_sheet.Cells(row, len(channel_categories) + 2).NumberFormat = "0.00%"

                # 计算传统媒体比例（线下媒体+视频广告）
                traditional_ratio = category_budgets.get("线下媒体", 0) + category_budgets.get("视频广告", 0)
                analysis_sheet.Cells(row, len(channel_categories) + 3).Value = traditional_ratio
                analysis_sheet.Cells(row, len(channel_categories) + 3).NumberFormat = "0.00%"

            # 添加业务洞察
            insight_start_row = len(results) + 8
            analysis_sheet.Cells(insight_start_row, 1).Value = "业务洞察与建议"
            analysis_sheet.Cells(insight_start_row, 1).Font.Bold = True

            # 找出最优方案
            best_result = max(results, key=lambda x: x["KPI"])

            insights = [
                f"• 最优方案: {best_result['方案名称']}，KPI提升 {best_result['提升比例']}",
                f"• 基准KPI: {baseline_kpi:,.0f}，最优KPI: {best_result['KPI']:,.0f}",
                "",
                "渠道效率分析:",
            ]

            # 添加渠道效率洞察
            top_channels = sorted(channel_performance, key=lambda x: x['improvement'], reverse=True)[:3]
            for i, perf in enumerate(top_channels, 1):
                insights.append(f"  {i}. {perf['channel']}: 效率最高，建议增加投入")

            # 添加预算分配建议
            insights.extend([
                "",
                "预算分配建议:",
                f"• 数字媒体占比建议: 40-60%（精准触达）",
                f"• 社交媒体占比建议: 20-35%（品牌互动）",
                f"• 传统媒体占比建议: 15-25%（品牌曝光）",
                f"• 赞助营销占比建议: 5-15%（品牌联想）"
            ])

            # 写入洞察内容
            for i, insight in enumerate(insights):
                analysis_sheet.Cells(insight_start_row + 1 + i, 1).Value = insight

            # 工作表5: 渠道效率分析
            efficiency_sheet = new_workbook.Worksheets.Add()
            efficiency_sheet.Name = "渠道效率分析"

            efficiency_sheet.Cells(1, 1).Value = "渠道效率分析"
            efficiency_sheet.Cells(1, 1).Font.Size = 14
            efficiency_sheet.Cells(1, 1).Font.Bold = True

            # 表头
            eff_headers = ["排名", "渠道名称", "单独测试KPI", "vs基准提升", "效率指数", "建议动作"]
            for i, header in enumerate(eff_headers, 1):
                cell = efficiency_sheet.Cells(3, i)
                cell.Value = header
                cell.Font.Bold = True

            # 渠道效率数据
            for rank, perf in enumerate(channel_performance, 1):
                row = rank + 3
                efficiency_sheet.Cells(row, 1).Value = rank
                efficiency_sheet.Cells(row, 2).Value = perf['channel']
                efficiency_sheet.Cells(row, 3).Value = perf['kpi']
                efficiency_sheet.Cells(row, 4).Value = perf['improvement']
                efficiency_sheet.Cells(row, 5).Value = perf['efficiency']

                # 根据效率给出建议
                if perf['improvement'] > 500:
                    suggestion = "强烈建议增加投入"
                elif perf['improvement'] > 100:
                    suggestion = "建议适度增加投入"
                elif perf['improvement'] > 0:
                    suggestion = "维持当前投入"
                elif perf['improvement'] > -100:
                    suggestion = "考虑减少投入"
                else:
                    suggestion = "建议大幅减少投入"

                efficiency_sheet.Cells(row, 6).Value = suggestion

            # 自动调整列宽
            try:
                for sheet in [overview_sheet, detail_sheet, raw_data_sheet, analysis_sheet, efficiency_sheet]:
                    sheet.Columns.AutoFit()
            except:
                pass

            # 保存文件
            output_path = os.path.abspath(output_filename)
            new_workbook.SaveAs(output_path)
            new_workbook.Close(SaveChanges=False)

            print(f"Excel文件已成功保存: {output_filename}")
            print(f"包含工作表: 优化结果总览, 详细预算分配, 原始输入数据, 业务对比分析, 渠道效率分析")
            return output_filename

        except Exception as excel_error:
            print(f"Excel导出失败: {excel_error}")
            print(f"已生成文本版报告: {text_file}")
            return text_file

    def optimize(self):
        """执行优化"""
        if not self.initialize_excel():
            raise Exception("无法初始化Excel连接")

        try:
            print(f"开始优化分析...")
            print(f"基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}")
            print(f"约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}")

            # 进度回调：初始化
            if self.progress_callback:
                self.progress_callback(5, "初始化Excel连接", "正在连接Excel...")

            # 获取基准KPI
            baseline_kpi, _ = self.get_baseline_kpi()
            print(f"基准方案KPI: {baseline_kpi:,.2f}")

            # 进度回调：基准计算完成
            if self.progress_callback:
                self.progress_callback(15, "基准方案计算完成", f"基准KPI: {baseline_kpi:,.2f}")

            # 测试各渠道表现
            print("正在测试各渠道单独表现...")
            if self.progress_callback:
                self.progress_callback(20, "测试各渠道表现", f"测试 {len(self.channels)} 个渠道...")

            channel_performance = self.test_individual_channels(baseline_kpi)

            # 显示渠道效率排名
            print(f"\n渠道效率排名（前5名）:")
            for i, perf in enumerate(channel_performance[:5], 1):
                print(f"{i}. {perf['channel']}: 提升 {perf['improvement']:+,.0f}")

            # 进度回调：渠道测试完成
            if self.progress_callback:
                self.progress_callback(40, "渠道测试完成", f"找到 {len([p for p in channel_performance if p['improvement'] > 0])} 个高效渠道")

            # 生成优化方案
            print(f"\n正在生成 {self.optimization_schemes} 个优化方案...")
            if self.progress_callback:
                self.progress_callback(45, "生成优化方案", "正在生成基础方案...")

            results = self.generate_optimization_schemes(baseline_kpi, channel_performance)

            # 显示最优方案
            if len(results) > 1:
                best_result = results[0] if results[0]["方案名称"] != "基准方案" else results[1]
                print(f"\n最优方案: {best_result['方案名称']}")
                print(f"KPI: {best_result['KPI']:,.2f}")
                print(f"提升: {best_result['提升']:+,.2f} ({best_result['提升比例']})")

                # 进度回调：优化完成
                if self.progress_callback:
                    self.progress_callback(95, "优化完成", f"最优提升: {best_result['提升比例']}")

            # 导出结果
            print("\n正在导出结果...")
            if self.progress_callback:
                self.progress_callback(98, "导出结果", "正在生成Excel报告...")

            output_file = self.export_results(results, baseline_kpi, channel_performance)

            # 进度回调：全部完成
            if self.progress_callback:
                self.progress_callback(100, "全部完成", f"已生成 {len(results)} 个方案")

            return {
                "results": results,
                "baseline_kpi": baseline_kpi,
                "channel_performance": channel_performance,
                "output_file": output_file
            }

        finally:
            self.cleanup_excel()
