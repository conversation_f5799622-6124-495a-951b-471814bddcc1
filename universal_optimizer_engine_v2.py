import win32com.client
import os
import time
import random
import numpy as np
from datetime import datetime

class UniversalOptimizerEngineV2:
    def __init__(self, config, channels):
        self.config = config
        self.channels = channels
        self.excel = None
        self.workbook = None
        self.worksheet = None
        
        # 从配置中获取参数
        self.excel_file = config["excel_file"]
        self.worksheet_name = config["worksheet_name"]
        self.input_start_row = config["input_start_row"]
        self.input_end_row = config["input_end_row"]
        self.input_start_col = config["input_start_col"]
        self.input_end_col = config["input_end_col"]
        self.output_col = config["output_col"]
        self.output_start_row = config.get("output_start_row", config["input_start_row"])
        self.output_end_row = config.get("output_end_row", config["input_end_row"])
        self.total_budget = config["total_budget"]
        self.optimization_schemes = config["optimization_schemes"]
        self.baseline_allocation = config["baseline_allocation"]
        self.custom_baseline_ratios = config.get("custom_baseline_ratios", {})
        self.channel_constraints = config.get("channel_constraints", {})
        
        # 默认约束范围：2%-60%
        self.default_min_ratio = 0.02
        self.default_max_ratio = 0.60
        
    def initialize_excel(self):
        """初始化Excel连接"""
        try:
            # 先尝试关闭可能存在的Excel进程
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
                time.sleep(1)
            except:
                pass

            # 创建新的Excel应用实例
            self.excel = win32com.client.Dispatch("Excel.Application")

            # 设置Excel属性（分步设置，便于调试）
            try:
                self.excel.DisplayAlerts = False
                self.excel.ScreenUpdating = False
                self.excel.Visible = False
            except Exception as e:
                print(f"设置Excel属性时出现警告: {e}")
                # 继续执行，这些属性设置失败通常不影响核心功能

            # 打开工作簿
            self.workbook = self.excel.Workbooks.Open(os.path.abspath(self.excel_file))
            self.worksheet = self.workbook.Worksheets(self.worksheet_name)

            print(f"Excel连接成功，工作表: {self.worksheet_name}")
            return True

        except Exception as e:
            print(f"初始化Excel失败: {e}")
            print("可能的解决方案:")
            print("1. 确保Excel文件未被其他程序占用")
            print("2. 检查文件路径是否正确")
            print("3. 确保Excel已正确安装")
            return False
    
    def cleanup_excel(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass
    
    def apply_constraints(self, budgets):
        """应用渠道约束"""
        constrained_budgets = budgets.copy()
        
        for i, channel in enumerate(self.channels):
            if channel in self.channel_constraints:
                constraint = self.channel_constraints[channel]
                min_ratio = constraint["min"]
                max_ratio = constraint["max"]
            else:
                # 使用默认约束
                min_ratio = self.default_min_ratio
                max_ratio = self.default_max_ratio
            
            min_budget = self.total_budget * min_ratio
            max_budget = self.total_budget * max_ratio
            
            constrained_budgets[i] = max(min_budget, min(max_budget, constrained_budgets[i]))
        
        # 重新归一化到总预算
        total_constrained = sum(constrained_budgets)
        if total_constrained > 0:
            constrained_budgets = [b * self.total_budget / total_constrained for b in constrained_budgets]
        
        return constrained_budgets

    def get_constraint_summary(self):
        """获取约束设置摘要"""
        if not self.channel_constraints:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有渠道"

        custom_count = len(self.channel_constraints)
        default_count = len(self.channels) - custom_count

        if custom_count == 0:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有{len(self.channels)}个渠道"
        elif default_count == 0:
            return f"所有{len(self.channels)}个渠道使用自定义约束"
        else:
            return f"{custom_count}个渠道使用自定义约束，{default_count}个渠道使用默认约束({self.default_min_ratio:.1%}-{self.default_max_ratio:.1%})"
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算并计算KPI"""
        try:
            # 应用约束
            constrained_budgets = self.apply_constraints(budgets)
            
            # 清空输入区域
            input_range = self.worksheet.Range(
                self.worksheet.Cells(self.input_start_row, self.input_start_col),
                self.worksheet.Cells(self.input_end_row, self.input_end_col)
            )
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.input_end_row - self.input_start_row + 1
            budgets_per_row = [budget / num_rows for budget in constrained_budgets]
            
            # 写入预算数据
            for row in range(self.input_start_row, self.input_end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_start_col + i
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.3)
            
            # 读取输出值并求和
            total_kpi = 0
            for row in range(self.output_start_row, self.output_end_row + 1):
                kpi_value = self.worksheet.Cells(row, self.output_col).Value
                if kpi_value is not None:
                    total_kpi += float(kpi_value)
            
            return total_kpi, constrained_budgets
        except Exception as e:
            print(f"计算KPI时出错: {e}")
            return 0, budgets
    
    def get_baseline_kpi(self):
        """获取基准方案的KPI"""
        if self.baseline_allocation == "equal":
            # 平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        elif self.baseline_allocation == "custom":
            # 自定义基准方案
            if not self.custom_baseline_ratios:
                raise ValueError("自定义基准配比未设置")
            
            equal_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0  # 转换为小数
                    equal_budgets.append(self.total_budget * ratio)
                else:
                    # 如果某个渠道没有设置，使用平均值
                    equal_budgets.append(self.total_budget / len(self.channels))
        else:
            # 默认平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        baseline_kpi, constrained_baseline = self.write_budgets_and_calculate(equal_budgets)
        return baseline_kpi, constrained_baseline
    
    def test_individual_channels(self, baseline_kpi):
        """测试各渠道单独表现"""
        channel_performance = []
        
        for i, channel in enumerate(self.channels):
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            kpi, _ = self.write_budgets_and_calculate(test_budgets)
            improvement = kpi - baseline_kpi
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'kpi': kpi,
                'improvement': improvement,
                'efficiency': improvement / (self.total_budget * 0.3)
            })
        
        # 按改进效果排序
        channel_performance.sort(key=lambda x: x['improvement'], reverse=True)
        return channel_performance
    
    def generate_optimization_schemes(self, baseline_kpi, channel_performance):
        """生成优化方案"""
        results = []
        
        # 添加基准方案
        if self.baseline_allocation == "equal":
            baseline_name = "基准方案（平均分配）"
            baseline_desc = f"所有渠道平均分配预算，应用{self.default_min_ratio:.1%}-{self.default_max_ratio:.1%}约束"
        else:
            baseline_name = "基准方案（自定义配比）"
            baseline_desc = f"使用自定义配比分配预算，应用{self.default_min_ratio:.1%}-{self.default_max_ratio:.1%}约束"
        
        baseline_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        if self.baseline_allocation == "custom":
            baseline_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0
                    baseline_budgets.append(self.total_budget * ratio)
                else:
                    baseline_budgets.append(self.total_budget / len(self.channels))
        
        results.append({
            "方案名称": baseline_name,
            "方案描述": baseline_desc,
            "KPI": baseline_kpi,
            "预算分配": dict(zip(self.channels, baseline_budgets)),
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 获取高效渠道
        top_channels = [perf['channel'] for perf in channel_performance[:6] if perf['improvement'] > 0]
        
        if len(top_channels) >= 2:
            # 方案1: 高效渠道集中
            budgets = [0.0] * len(self.channels)
            high_budget = self.total_budget * 0.6 / len(top_channels[:3])
            low_budget = self.total_budget * 0.4 / (len(self.channels) - 3)
            
            for i, channel in enumerate(self.channels):
                if channel in top_channels[:3]:
                    budgets[i] = high_budget
                else:
                    budgets[i] = low_budget
            
            kpi, final_budgets = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi
            improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0
            
            results.append({
                "方案名称": "高效渠道集中方案",
                "方案描述": "重点投放前3个高效渠道，其他渠道保持基础投放",
                "KPI": kpi,
                "预算分配": dict(zip(self.channels, final_budgets)),
                "提升": improvement,
                "提升比例": f"{improvement_pct:.2f}%"
            })
        
        # 方案2: 渐进式优化
        budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        for i, channel in enumerate(self.channels):
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf:
                rank = channel_performance.index(perf) + 1
                if rank <= 3:
                    budgets[i] *= 1.5
                elif rank <= 6:
                    budgets[i] *= 1.2
                elif rank > 10:
                    budgets[i] *= 0.7
        
        # 重新归一化
        total_check = sum(budgets)
        budgets = [b * self.total_budget / total_check for b in budgets]
        
        kpi, final_budgets = self.write_budgets_and_calculate(budgets)
        improvement = kpi - baseline_kpi
        improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0
        
        results.append({
            "方案名称": "渐进式优化方案",
            "方案描述": "根据渠道效率逐步调整预算分配",
            "KPI": kpi,
            "预算分配": dict(zip(self.channels, final_budgets)),
            "提升": improvement,
            "提升比例": f"{improvement_pct:.2f}%"
        })
        
        # 方案3-N: 智能搜索优化（增加迭代次数提高uplift）
        best_random_results = []
        max_iterations = max(500, self.optimization_schemes * 50)  # 增加迭代次数

        print(f"正在进行智能搜索优化（{max_iterations}次迭代）...")

        best_kpi_so_far = baseline_kpi
        no_improvement_count = 0

        for iteration in range(max_iterations):
            # 显示进度
            if iteration % 50 == 0:
                progress = (iteration / max_iterations) * 100
                print(f"  搜索进度: {progress:.1f}% ({iteration}/{max_iterations})")

            # 生成随机权重，偏向高效渠道
            weights = [random.uniform(0.1, 3.0) for _ in self.channels]

            # 根据渠道表现调整权重
            for i, channel in enumerate(self.channels):
                perf = next((p for p in channel_performance if p['channel'] == channel), None)
                if perf and perf['improvement'] > 0:
                    rank = channel_performance.index(perf) + 1
                    if rank <= 3:
                        weights[i] *= 2.5  # 增加权重倍数
                    elif rank <= 6:
                        weights[i] *= 1.8
                    elif rank <= 10:
                        weights[i] *= 1.2

            # 添加一些随机性避免局部最优
            if iteration > max_iterations * 0.3:  # 后期增加更多随机性
                for i in range(len(weights)):
                    weights[i] *= random.uniform(0.8, 1.2)

            # 归一化权重
            total_weight = sum(weights)
            ratios = [w / total_weight for w in weights]
            budgets = [r * self.total_budget for r in ratios]

            kpi, final_budgets = self.write_budgets_and_calculate(budgets)
            improvement = kpi - baseline_kpi

            # 记录好的结果
            if improvement > 0:
                improvement_pct = (improvement / baseline_kpi) * 100

                # 检查是否是新的最佳结果
                if kpi > best_kpi_so_far:
                    best_kpi_so_far = kpi
                    no_improvement_count = 0
                    print(f"    发现更好结果: KPI = {kpi:,.2f} (+{improvement_pct:.2f}%)")
                else:
                    no_improvement_count += 1

                # 只保存足够好的结果
                if improvement_pct > 0.5:  # 至少0.5%的提升
                    best_random_results.append({
                        "方案名称": f"智能搜索方案{len(best_random_results)+1}",
                        "方案描述": f"通过{iteration+1}次迭代搜索得到的优化方案",
                        "KPI": kpi,
                        "预算分配": dict(zip(self.channels, final_budgets)),
                        "提升": improvement,
                        "提升比例": f"{improvement_pct:.2f}%"
                    })

            # 早停机制：如果很长时间没有改进，提前结束
            if no_improvement_count > 100 and len(best_random_results) >= self.optimization_schemes:
                print(f"  提前结束搜索（{iteration+1}次迭代后无显著改进）")
                break

        print(f"智能搜索完成，找到 {len(best_random_results)} 个有效方案")

        # 按KPI排序，取前几个
        best_random_results.sort(key=lambda x: x["KPI"], reverse=True)
        results.extend(best_random_results[:self.optimization_schemes-2])
        
        # 按KPI排序所有结果
        results.sort(key=lambda x: x["KPI"], reverse=True)

        return results

    def export_results(self, results, baseline_kpi, channel_performance):
        """导出结果到新文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"媒体预算优化结果_v2_{timestamp}.xlsx"

            # 创建新的Excel文件
            new_excel = win32com.client.Dispatch("Excel.Application")
            new_excel.Visible = False
            new_excel.DisplayAlerts = False

            try:
                new_workbook = new_excel.Workbooks.Add()

                # 工作表1: 结果总览
                overview_sheet = new_workbook.Worksheets(1)
                overview_sheet.Name = "优化结果总览"

                # 写入标题和基本信息（美化格式）
                overview_sheet.Cells(1, 1).Value = "📊 媒体预算优化分析报告 v2.0"
                overview_sheet.Cells(1, 1).Font.Size = 18
                overview_sheet.Cells(1, 1).Font.Bold = True
                overview_sheet.Cells(1, 1).Font.Color = 0x0066CC  # 蓝色
                overview_sheet.Range("A1:F1").Merge()

                # 添加分隔线
                overview_sheet.Cells(2, 1).Value = "=" * 80
                overview_sheet.Cells(2, 1).Font.Color = 0x808080  # 灰色

                # 基本信息（带图标）
                overview_sheet.Cells(3, 1).Value = f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                overview_sheet.Cells(4, 1).Value = f"📈 基准方案KPI: {baseline_kpi:,.2f}"
                overview_sheet.Cells(5, 1).Value = f"💰 总预算: ¥{self.total_budget:,}"

                # 显示实际约束范围
                constraint_info = self.get_constraint_summary()
                overview_sheet.Cells(6, 1).Value = f"🔒 约束设置: {constraint_info}"
                overview_sheet.Cells(7, 1).Value = f"⚙️ 基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}"

                # 设置基本信息的格式
                for row in range(3, 8):
                    overview_sheet.Cells(row, 1).Font.Size = 11

                # 结果表头（美化）
                headers = ["🏆 排名", "📋 方案名称", "📝 方案描述", "📊 KPI", "📈 提升", "📊 提升比例"]
                for i, header in enumerate(headers, 1):
                    cell = overview_sheet.Cells(9, i)
                    cell.Value = header
                    cell.Font.Bold = True
                    cell.Font.Size = 12
                    cell.Interior.Color = 0xE6F3FF  # 浅蓝色背景
                    cell.Borders.Weight = 2  # 边框

                # 写入结果数据（美化）
                for rank, result in enumerate(results, 1):
                    row = rank + 9

                    # 排名（添加奖牌图标）
                    rank_icon = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else str(rank)
                    overview_sheet.Cells(row, 1).Value = rank_icon

                    overview_sheet.Cells(row, 2).Value = result["方案名称"]
                    overview_sheet.Cells(row, 3).Value = result["方案描述"]
                    overview_sheet.Cells(row, 4).Value = result["KPI"]
                    overview_sheet.Cells(row, 4).NumberFormat = "#,##0.00"  # 数字格式

                    overview_sheet.Cells(row, 5).Value = result["提升"]
                    overview_sheet.Cells(row, 5).NumberFormat = "#,##0.00"

                    overview_sheet.Cells(row, 6).Value = result["提升比例"]

                    # 根据提升比例设置颜色
                    improvement_pct = float(result["提升比例"].replace("%", ""))
                    if improvement_pct > 5:
                        overview_sheet.Cells(row, 6).Font.Color = 0x008000  # 绿色
                    elif improvement_pct > 2:
                        overview_sheet.Cells(row, 6).Font.Color = 0x0066CC  # 蓝色
                    elif improvement_pct > 0:
                        overview_sheet.Cells(row, 6).Font.Color = 0x666666  # 灰色

                    # 为最优方案添加背景色
                    if rank == 1 and improvement_pct > 0:
                        for col in range(1, 7):
                            overview_sheet.Cells(row, col).Interior.Color = 0xE6FFE6  # 浅绿色

                # 工作表2: 详细预算分配
                detail_sheet = new_workbook.Worksheets.Add()
                detail_sheet.Name = "详细预算分配"

                detail_headers = ["方案名称", "KPI", "提升比例"] + self.channels
                for i, header in enumerate(detail_headers, 1):
                    detail_sheet.Cells(1, i).Value = header
                    detail_sheet.Cells(1, i).Font.Bold = True

                for rank, result in enumerate(results, 1):
                    row = rank + 1
                    detail_sheet.Cells(row, 1).Value = result["方案名称"]
                    detail_sheet.Cells(row, 2).Value = result["KPI"]
                    detail_sheet.Cells(row, 3).Value = result["提升比例"]

                    for i, channel in enumerate(self.channels):
                        budget = result["预算分配"][channel]
                        ratio = budget / self.total_budget
                        detail_sheet.Cells(row, 4 + i).Value = ratio
                        detail_sheet.Cells(row, 4 + i).NumberFormat = "0.00%"

                # 工作表3: 渠道效率分析
                channel_sheet = new_workbook.Worksheets.Add()
                channel_sheet.Name = "渠道效率分析"

                channel_headers = ["排名", "渠道名称", "单独测试KPI", "vs基准提升", "效率"]
                for i, header in enumerate(channel_headers, 1):
                    channel_sheet.Cells(1, i).Value = header
                    channel_sheet.Cells(1, i).Font.Bold = True

                for rank, perf in enumerate(channel_performance, 1):
                    row = rank + 1
                    channel_sheet.Cells(row, 1).Value = rank
                    channel_sheet.Cells(row, 2).Value = perf['channel']
                    channel_sheet.Cells(row, 3).Value = perf['kpi']
                    channel_sheet.Cells(row, 4).Value = perf['improvement']
                    channel_sheet.Cells(row, 5).Value = perf['efficiency']

                # 工作表4: 原始输入数据
                raw_data_sheet = new_workbook.Worksheets.Add()
                raw_data_sheet.Name = "原始输入数据"

                raw_data_sheet.Cells(1, 1).Value = "原始输入数据（供Excel验证）"
                raw_data_sheet.Cells(1, 1).Font.Bold = True
                raw_data_sheet.Cells(2, 1).Value = "说明：以下数据可直接复制到Excel进行验证"
                raw_data_sheet.Cells(3, 1).Value = f"输入区域: {chr(64+self.input_start_col)}{self.input_start_row}:{chr(64+self.input_end_col)}{self.input_end_row}"
                raw_data_sheet.Cells(4, 1).Value = f"输出区域: {chr(64+self.output_col)}{self.output_start_row}:{chr(64+self.output_col)}{self.output_end_row}"

                raw_data_sheet.Cells(6, 1).Value = "方案名称"
                for i, channel in enumerate(self.channels, 2):
                    raw_data_sheet.Cells(6, i).Value = channel

                for rank, result in enumerate(results[:5], 1):  # 只输出前5个方案
                    row = rank + 6
                    raw_data_sheet.Cells(row, 1).Value = result["方案名称"]

                    budgets = [result["预算分配"][ch] for ch in self.channels]
                    num_rows = self.input_end_row - self.input_start_row + 1
                    budgets_per_row = [budget / num_rows for budget in budgets]

                    for i, budget_per_row in enumerate(budgets_per_row, 2):
                        raw_data_sheet.Cells(row, i).Value = budget_per_row
                        raw_data_sheet.Cells(row, i).NumberFormat = "0.00"

                # 自动调整所有工作表的列宽
                for sheet in [overview_sheet, detail_sheet, channel_sheet, raw_data_sheet]:
                    sheet.Columns.AutoFit()

                # 保存文件
                output_path = os.path.abspath(output_filename)
                new_workbook.SaveAs(output_path)
                new_workbook.Close()

                return output_filename

            finally:
                new_excel.Quit()

        except Exception as e:
            print(f"导出结果时出错: {e}")
            return None

    def optimize(self):
        """执行优化"""
        if not self.initialize_excel():
            raise Exception("无法初始化Excel连接")

        try:
            print(f"开始优化分析...")
            print(f"基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}")
            print(f"约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}")

            # 获取基准KPI
            baseline_kpi, _ = self.get_baseline_kpi()
            print(f"基准方案KPI: {baseline_kpi:,.2f}")

            # 测试各渠道表现
            print("正在测试各渠道单独表现...")
            channel_performance = self.test_individual_channels(baseline_kpi)

            # 显示渠道效率排名
            print(f"\n渠道效率排名（前5名）:")
            for i, perf in enumerate(channel_performance[:5], 1):
                print(f"{i}. {perf['channel']}: 提升 {perf['improvement']:+,.0f}")

            # 生成优化方案
            print(f"\n正在生成 {self.optimization_schemes} 个优化方案...")
            results = self.generate_optimization_schemes(baseline_kpi, channel_performance)

            # 显示最优方案
            if len(results) > 1:
                best_result = results[0] if results[0]["方案名称"] != "基准方案" else results[1]
                print(f"\n最优方案: {best_result['方案名称']}")
                print(f"KPI: {best_result['KPI']:,.2f}")
                print(f"提升: {best_result['提升']:+,.2f} ({best_result['提升比例']})")

            # 导出结果
            print("\n正在导出结果...")
            output_file = self.export_results(results, baseline_kpi, channel_performance)

            return {
                "results": results,
                "baseline_kpi": baseline_kpi,
                "channel_performance": channel_performance,
                "output_file": output_file
            }

        finally:
            self.cleanup_excel()
